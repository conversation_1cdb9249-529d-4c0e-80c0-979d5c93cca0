import Section1 from "@/Components/AI/Section1";
import Section2 from "@/Components/DataEngineering/Section2";
import Section3 from "@/Components/DataEngineering/Section3";
import Section4 from "@/Components/DataEngineering/Section4";
import Section5 from "@/Components/DataEngineering/Section5";
import Section6 from "@/Components/DataEngineering/Section6";
import Section7 from "@/Components/DataEngineering/Section7";
import Faq from "@/Components/Faq/Faq";

import Image from "next/image";
import React from "react";
export const metadata = {
  title: "Thrive through data with next-gen data engineering solutions",
  description: "Valueans data engineering services & solutions boost data quality, maximize the use of large data, protect information, and get a scalable infrastructure.",
  alternates: {
    canonical: "https://valueans.com/DataEngineering",
  },
};
const page = () => {
   const accordionData = [
    {
      title: "Why are data engineering services necessary for modern businesses?",
      content: "To effectively manage and interpret the massive volumes of data that businesses create daily, data engineering is essential. To maintain a competitive edge and support business in a constantly evolving digital environment, data engineering guarantees the quality, scalability, and security of data.",
    },
    {
      title: "Why does a business need data engineering?",
      content: "Implementing and running data pipelines and infrastructure inside an organization is the main emphasis of the field of data engineering. It guarantees that enterprise data is current across the data ecosystem, flows effectively, and is appropriately integrated and managed.",
    },
    {
      title: "Which issues are resolved by data engineering?",
      content: "Data engineers provide dependable ETL procedures to address data integration issues. They start by thoroughly comprehending the formats, data sources, and business needs. To ensure interoperability, they pull data from a variety of sources using technologies like Informatica or Apache Spark.",
    },
    {
      title: "Is it possible to automate data engineering?",
      content: "For businesses to efficiently manage the growing complexity and volume of data, data engineering automation is essential. Businesses may significantly improve efficiency and scalability by automating critical processes like data pipelines, quality assurance, and integration.",
    },
    {
      title: "How do data engineers gather information?",
      content: "Any company's primary data collecting strategy usually involves gathering information from its own website or application. Working with engineering teams is crucial when collecting this kind of data to make sure that everything is tracked in a way that data analysts and analytics engineers can use. ",
    },
    {
      title: "What is the data engineering life cycle?",
      content: "A technique for managing data engineering procedures, such as data collection, integration, storage, processing, and analysis, is the data engineering lifecycle. In order to continuously provide high-quality data engineering projects, this lifecycle consists of organized and related stages.",
    },
  ];
  return (
    <>
      <Section1
        backgroundImage={"/Images/DE-bg.jpeg"}
        heading={"Data Engineering Solutions "}
        bannerText={"Governance-Focused Data Engineering Services to Turn Complexity into Clarity"}
      />
      <Section2 />
      <Section3 />
      <Section4 />
      <Section5 />
      <Section6 />
      <Section7 />
      <Faq content={accordionData}/>
    </>
  );
};

export default page;
