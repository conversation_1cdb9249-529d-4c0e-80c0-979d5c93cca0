"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const Technologies = ({ onNavigate }) => {
  // Enhanced navigation handler for dropdown items
  const handleEnhancedNavigation = (href, event) => {
    // Allow right-click (context menu) - don't prevent default
    if (event.button === 2) {
      return;
    }

    // Allow Ctrl+click (or Cmd+click on Mac) to open in new tab
    if (event.ctrlKey || event.metaKey) {
      window.open(href, '_blank');
      return;
    }

    // For normal left-click, prevent default and use custom navigation
    event.preventDefault();
    onNavigate(href);
  };
  return (
    <div className="md:container md:mx-auto md:p-10 md:border md:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Image Section */}
        <div className="col-span-1 md:col-span-2">
          <Image className="hidden md:block" src={"/Images/tech-nav.jpeg"} width={400} height={400} alt="Technologies" />
        </div>

        {/* Links Section */}
        <div className="col-span-1 md:col-span-3 space-y-4 md:space-y-0 md:flex md:justify-between">
          {/* Column 1 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/Technologies/Progressive_Web_Apps"
              onClick={(e) => handleEnhancedNavigation("/Technologies/Progressive_Web_Apps", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/Progressive_Web_Apps", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Progressive Web Apps
            </Link>
            <Link
              href="/Technologies/NLP"
              onClick={(e) => handleEnhancedNavigation("/Technologies/NLP", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/NLP", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              NLP
            </Link>
            <Link
              href="/Technologies/Low_Code_Development"
              onClick={(e) => handleEnhancedNavigation("/Technologies/Low_Code_Development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/Low_Code_Development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Low Code Deployment
            </Link>
          </div>

          {/* Column 2 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/Technologies/AR_VR"
              onClick={(e) => handleEnhancedNavigation("/Technologies/AR_VR", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/AR_VR", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              AR/VR
            </Link>
            <Link
              href="/Technologies/MicroServices"
              onClick={(e) => handleEnhancedNavigation("/Technologies/MicroServices", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/MicroServices", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Microservices
            </Link>
            <Link
              href="/Technologies/Predictive_Analysis"
              onClick={(e) => handleEnhancedNavigation("/Technologies/Predictive_Analysis", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/Predictive_Analysis", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Predictive Analytics
            </Link>
          </div>

          {/* Column 3 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/Technologies/IOT"
              onClick={(e) => handleEnhancedNavigation("/Technologies/IOT", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/IOT", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              IoT
            </Link>
            <Link
              href="/Technologies/AI_ML"
              onClick={(e) => handleEnhancedNavigation("/Technologies/AI_ML", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/AI_ML", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              AI/ML
            </Link>
            <Link
              href="/Technologies/Cross_Platform_And_Hybrid_Development"
              onClick={(e) => handleEnhancedNavigation("/Technologies/Cross_Platform_And_Hybrid_Development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Technologies/Cross_Platform_And_Hybrid_Development", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Cross-platform and Hybrid Development
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Technologies;
