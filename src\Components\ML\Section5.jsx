import React from "react";
import Image from "next/image";

const Card = ({ title, description, imgSrc, altText }) => {
  return (
    <div className="block w-full md:max-w-sm h-auto md:h-[300px] p-3 bg-white border border-purple-500 rounded-md shadow overflow-hidden">
      <div className="flex items-center gap-2">
        <Image src={imgSrc} alt={altText} width={46} height={46} />
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>
      <p className="text-sm md:text-base text-justify mt-2">{description}</p>
    </div>
  );
};

const cardData = [
  {
    title: "Healthcare",
    description:
      "Our custom machine learning solutions improve results and operational efficiency by streamlining patient care, personalizing treatment regimens, and enhancing diagnostics. Better patient care is ensured by our sophisticated algorithms, which also make predictive analytics and early illness identification easier.",
    imgSrc: "/Images/Healthcare-icon.png",
    altText: "Healthcare Icon",
  },
  {
    title: "Fintech",
    description:
      "Our machine learning app development services enable fintech businesses to develop and remain competitive by providing safe transactions, risk management, and individualized financial services. To protect financial activities, we also provide real-time risk assessment and fraud detection.",
    imgSrc: "/Images/finance-symbol.png",
    altText: "Finance Icon",
  },
  {
    title: "Ecommerce",
    description:
      "By improving digital presence and functionality, we enhance inventory management, customize consumer experiences, and increase sales through sophisticated recommendation systems. Businesses can better grasp customer trends and behavior with our data-driven insights.",
    imgSrc: "/Images/ecommerce.png",
    altText: "Ecommerce Icon",
  },
  {
    title: "E-Learning",
    description:
      "Our machine learning solutions development improves student engagement, customizes instruction, and expedites administrative work, increasing the effectiveness and accessibility of education. Technologies for adaptive learning that customize course material to each student's needs are supported by us.",
    imgSrc: "/Images/e_learning.png",
    altText: "E-Learning Icon",
  },
  {
    title: "Real Estate",
    description:
      "We give real estate professionals information on consumer preferences, market trends, and property valuation to help them make better decisions and engage with clients. Finding profitable investment options is made easier with the help of our prediction algorithms.",
    imgSrc: "/Images/real-estate.png",
    altText: "Real Estate Icon",
  },
  {
    title: "Fitness",
    description:
      "We develop individualized training programs, online coaching, and health tracking tools to assist people in reaching their fitness objectives and leading better lives. To inspire users, our ML-powered apps provide progress monitoring and real-time feedback.",
    imgSrc: "/Images/fitness-watch.png",
    altText: "Fitness Icon",
  },
];

const Section5 = () => {
  return (
    <>
      <section className="bg-blue-100 mb-10 md:mb-24 py-6 md:py-[42px]">
        <div className="container">
          <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
            We’ve Delivered Successful{" "}   
            <span className="text-[#F245A1]">
              Machine Learning`
               Solutions
            </span>{" "}
            to Many Industries.
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-6 my-3 md:my-6">
            {cardData.map((card, index) => (
              <Card
                key={index}
                title={card.title}
                description={card.description}
                imgSrc={card.imgSrc}
                altText={card.altText}
              />
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default Section5;
