import React from "react";
import HorizontalColoredCard from "./HorizontalColoredCard";

const Section10 = () => {
  const listContent = [
    "75% increase in number of tweets sent.",
    "65% increase in number of pages visited per session.",
    "Improved performance on low-bandwidth networks",
  ];
  return (
    <div className="container my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        <span className="text-[#F245A1]">Case Studies</span> Notable Companies
        That Have Embraced PWAs 
      </h2>
      <div className="items-center gap-3 md:gap-6 my-[32px] mx-[20px]">
        <div className="bg-[#F245A126] flex justify-center p-4 md:p-8">
          <HorizontalColoredCard
            heading={"1. Twitter Lite"}
            paragraph={
              "In order to cater to users’ needs, Twitter undertook the creation of a PWA to provide a more lightweight and speedier version of its app. Because of this, there was a: "
            }
            listContent={listContent}
            bgColor={"bg-[#794CEC26] md:w-[60%]"}
          />
        </div>
        <div className="flex flex-col gap-3 md:flex-row md:justify-between  items-center mt-5 bg-[#794CEC26] p-4 md:p-8">
          <HorizontalColoredCard
            heading={"2. Uber "}
            paragraph={
              "Uber also leveraged the valuable benefits of PWAs with user-centric designs. Their PWA makes it easy for users to book rides even on slow networks. The interface is tailored for 2G and 3G users for the convenience of as many possible users. "
            }
            bgColor={"bg-[#794CEC26] md:w-[45%]"}
          />
          <HorizontalColoredCard
            heading={"3. Spotify "}
            paragraph={
              "The shift to a progressive web app increased the loading speed of the site on Spotify and improved user interaction as well as giving more users the chance to enjoy instant music access."
            }
            bgColor={"bg-[#F245A126] md:w-[45%]"}
          />
        </div>
      </div>
    </div>
  );
};

export default Section10;
