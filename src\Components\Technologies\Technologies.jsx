"use client";
import React, { useState } from "react";
import Image from "next/image";

const technologies = [
  { name: "Code Deployment", image: "/Images/coding.png" },
  { name: "NLP", image: "/Images/natural-language-processing.png" },
  { name: "AR & VR", image: "/Images/virtual-reality.png" },
  { name: "Microservices", image: "/Images/microservice.png" },
  { name: "IOT", image: "/Images/iot.png" },
  { name: "Saas", image: "/Images/saas.png" },
  { name: "AI As A Service", image: "/Images/artificial-intelligence.png" },
  { name: "Automated Machine Learning", image: "/Images/machine-learning.png" },
  { name: "Predictive Analytics", image: "/Images/data-analysis.png" },
  { name: "Progressive Web Apps", image: "/Images/app-development.png" },
  { name: "Cross-platform & Hybrid Development", image: "/Images/hybrid-development.png" },
];

const TechnologiesBanner = () => {
  const [hoveredIndex, setHoveredIndex] = useState(null);

  return (
    <div className="container py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="text-center text-2xl md:text-3xl font-semibold text-[#7716BC] mb-12">
          Technologies
        </h2>

        {/* Banner Container */}
        <div className="relative overflow-hidden">
          {/* Scrolling Banner */}
          <div className="flex animate-scroll space-x-8 md:space-x-12">
            {/* First set of technologies */}
            {technologies.map((tech, index) => (
              <div
                key={`first-${index}`}
                className={`flex items-center space-x-3 px-6 py-4 bg-white rounded-lg border-2 border-transparent transition-all duration-300 ease-in-out cursor-pointer flex-shrink-0 ${
                  hoveredIndex === index
                    ? "transform scale-110 border-[#7716BC] shadow-lg"
                    : "hover:border-gray-200 hover:shadow-md"
                }`}
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="flex items-center justify-center w-8 h-8 md:w-10 md:h-10">
                  <Image
                    src={tech.image}
                    alt={tech.name}
                    width={32}
                    height={32}
                    className="object-contain"
                  />
                </div>
                <span className="text-sm md:text-base font-medium text-gray-700 whitespace-nowrap">
                  {tech.name}
                </span>
              </div>
            ))}

            {/* Duplicate set for seamless scrolling */}
            {technologies.map((tech, index) => (
              <div
                key={`second-${index}`}
                className={`flex items-center space-x-3 px-6 py-4 bg-white rounded-lg border-2 border-transparent transition-all duration-300 ease-in-out cursor-pointer flex-shrink-0 ${
                  hoveredIndex === index + technologies.length
                    ? "transform scale-110 border-[#7716BC] shadow-lg"
                    : "hover:border-gray-200 hover:shadow-md"
                }`}
                onMouseEnter={() => setHoveredIndex(index + technologies.length)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="flex items-center justify-center w-8 h-8 md:w-10 md:h-10">
                  <Image
                    src={tech.image}
                    alt={tech.name}
                    width={32}
                    height={32}
                    className="object-contain"
                  />
                </div>
                <span className="text-sm md:text-base font-medium text-gray-700 whitespace-nowrap">
                  {tech.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Custom CSS for animation */}
      <style jsx>{`
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }

        .animate-scroll {
          animation: scroll 30s linear infinite;
        }

        .animate-scroll:hover {
          animation-play-state: paused;
        }
      `}</style>
    </div>
  );
};

export default TechnologiesBanner;
