import Image from "next/image";
import React from "react";

const Container = ({ children, bgColor = "bg-white", className = "" }) => {
  return (
    <div className={`w-full p-3 md:px-[42px] md:py-6 ${bgColor} ${className}`}>
      {children}
    </div>
  );
};

const Bulletpoint = ({ Content, bgColor = "bg-white" }) => {
  return (
    <div
      className={`max-w-lg p-2  flex justify-start items-start ${bgColor} border border-purple-500 rounded-md shadow-sm`}
    >
      <Image src={"/Images/pointer.png"} alt="pointer" className="flex-shrink-0" width={32} height={32} />
      <p
        className="text-sm md:text-base text-justify"
        dangerouslySetInnerHTML={{ __html: Content }}
      />
    </div>
  );
};

const Content = ({ title, description }) => {
  return (
    <div>
      <h3 className="text-lg md:text-2xl font-semibold mb-1 md:mb-3">
        {title}
      </h3>
      {description && (
        <p
          className="text-base md:text-lg text-justify"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
    </div>
  );
};

const Section4 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold mb-[24px] md:mb-[42px]">
        <span className="text-[#7716BC]"> Valueans </span> Telehealth Software{" "}
        Services
      </h2>

      <Container
        className="flex flex-col md:flex-row  justify-between gap-8 md:gap-[119px]"
        bgColor="bg-pink-100"
      >
        <div>
          <Content
            title={"EMR/EHR"}
            description={
              "By giving healthcare providers the ability to obtain, store, manage, and transfer patient records between specialists, departments, and institutions, we provide electronic health records and electronic medical record software that improves interoperability, efficiency, and patient care."
            }
          />
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={
                "Combining patient data from patient devices and medical software"
              }
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={
                "Appointment scheduling and automatic reminders about upcoming appointments"
              }
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Speech recognition features to speed up data entry"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={
                "Medical history, treatment plans, and test results for the patient"
              }
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Complete business integration"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Electronic prescriptions"}
              bgColor="bg-purple-200"
            />
          </div>
        </div>
        <div>
          <Content
            title={"Healthcare Mobile Apps"}
            description={
              "We deploy mobile app versions of current medical software or stand-alone healthcare solutions for patients, healthcare providers, and organizations. We can add a variety of unique features to mobile medical apps and combine them with wearable technology and third-party systems, depending on your particular needs."
            }
          />
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"EHR and e-prescribing accessibility"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Management of Appointments"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Monitoring of patients remotely"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Training and Education"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Consultations conducted online"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Collaboration of the medical team"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Monitoring health data in real time"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Alerts and reminders"}
              bgColor="bg-pink-200"
            />
          </div>
        </div>
      </Container>
      <Container
        className="flex flex-col md:flex-row  justify-between gap-8 md:gap-[119px] my-6"
        bgColor="bg-purple-100"
      >
        <div>
          <Content
            title={"CRM for Healthcare"}
            description={
              "Valueans provides CRM software that is customized to meet the specific clinical and business needs of healthcare professionals. By automating their marketing and sales processes, our completely compliant and secure CRMs assist medical enterprises in providing flawless patient experiences."
            }
          />
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"Profiling of patients"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Automation of marketing and sales"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Management of referrals"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Management of customer data"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Collaboration and task management"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"<a href='/Data_and_Analytics' class='text-[#7716BC] hover:underline'>Analytics and Reporting</a>"}
              bgColor="bg-purple-200"
            />
          </div>
        </div>
        <div>
          <Content
            title={"Hospital Management Software (HMS)"}
            description={
              "We provide healthcare IT solutions that enhance internal procedures, simplify the management of patients, data, personnel, and inventory, ease cross-departmental and financial operations, and offer analytical insights into hospital productivity."
            }
          />
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"Management of the supply chain"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Management of the ward"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Management of medications and inventory"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Management of patients and contracts"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"<a href='/financial-app-development' class='text-[#7716BC] hover:underline'>Financial management</a> and medical bills"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Management of insurance claims"}
              bgColor="bg-pink-200"
            />
          </div>
        </div>
      </Container>
      <Container
        className="flex flex-col md:flex-row  justify-between gap-8 md:gap-[119px]"
        bgColor="bg-pink-100"
      >
        <div>
          <Content
            title={"Software for Medical Devices"}
            description={
              "Valueans develops a custom healthcare software development solution that is compatible with medical equipment that is subject to the strictest regulations. We develop systems that can operate medical devices independently (SaMD) and apps to power and control diagnostic, monitoring, and therapy devices."
            }
          />
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"Wearables for patients"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Defibrillators and inhalers"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Intelligent carts"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Devices for collecting blood"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"RFID readers, biometrics, and barcode scanners"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Heart board, temperature, and humidity sensors"}
              bgColor="bg-purple-200"
            />
          </div>
        </div>
        <div>
          <Content
            title={"Healthcare eCommerce"}
            description={
              "Valueans implements customized solutions for online pharmacies, medical equipment stores, healthcare marketplaces, and medical service websites and integrates them with other healthcare software solutions to assist <a href='/Industries/Healthcare' class='text-[#7716BC] hover:underline'>healthcare industries</a> in making money off of their services."
            }
          />
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"Management of Products"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Marketing and interactions with customers"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Customer service across all channels"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint Content={"Order processing"} bgColor="bg-pink-200" />
          </div>
        </div>
      </Container>
      <Container
        className="flex flex-col md:flex-row  justify-between gap-8 md:gap-[119px] my-6"
        bgColor="bg-purple-100"
      >
        <div>
          <Content
            title={"Telehealth"}
            description={
              "Our specialists develop telehealth software solutions that, while maintaining the confidentiality of medical data, allow healthcare companies to offer remote medical services, such as diagnosis and treatment, or more interesting online medical education and training for medical staff."
            }
          />
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"Consultations with doctors and online care"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Safe communication and sharing of images"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Administration of patient data"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Patient monitoring via remote"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Getting a second opinion"}
              bgColor="bg-purple-200"
            />
          </div>
        </div>
        <div>
          <Content
            title={"Solutions for Remote Patient Monitoring"}
            description={
              "By safely gathering, storing, processing, and transmitting patient health data, Valueans provides software solutions for all kinds of health data collection devices that support medical practitioners in improving the standard of medical assistance."
            }
          />
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"Management of population health"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"After-discharge care coordination"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Home-based hospital"}
              bgColor="bg-pink-200"
            />
            <Bulletpoint Content={"Remote diagnostics"} bgColor="bg-pink-200" />
          </div>
        </div>
      </Container>
      <Container
        className="flex flex-col md:flex-row  justify-between gap-8 md:gap-[119px] my-6"
        bgColor="bg-pink-100"
      >
        <div className="flex-1">
          <Content
            title={"Information Management Systems for Laboratories"}
            description={
              "We use LIMS systems to streamline scientific and administrative procedures, speed up the digital transformation of daily laboratory operations, and enable information sharing between laboratories and other healthcare institutions."
            }
          />
        </div>
        <div className="flex-1 flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
          <Bulletpoint
            Content={"Management of samples"}
            bgColor="bg-purple-200"
          />
          <Bulletpoint
            Content={"Management of inventories"}
            bgColor="bg-purple-200"
          />
          <Bulletpoint
            Content={"Automation of workflows"}
            bgColor="bg-purple-200"
          />
          <Bulletpoint
            Content={"Management and analysis of data"}
            bgColor="bg-purple-200"
          />
        </div>
      </Container>
    </div>
  );
};

export default Section4;
