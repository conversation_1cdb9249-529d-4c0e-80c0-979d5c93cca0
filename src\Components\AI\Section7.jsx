import React from "react";
import Image from "next/image";
import ChooseCard from "./ChooseCard";

const Section7 = () => {
  return (
    <section className="bg-pink-100 my-10 md:my-24 p-4 md:p-10">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-3 my-3 gap-8 md:my-6">
          <div className="md:col-span-2">
            <h3 className="text-xl md:text-3xl md:leading-[57px] font-bold">
              Why Choose Valueans for{" "}
              <span className="text-[#F245A1]">AI Solutions</span> Development
            </h3>
            <p className="text-base md:text-xl text-justify">
             We’ve provided the most effective AI solutions to several companies across the globe. Our enterprise AI solutions have helped companies streamline their complex processes, bring efficiency to their work, and increase ROI. We leverage the most advanced technologies to bring the most innovative AI solutions to your business.
            </p>
          </div>
          <div className="md:col-span-1 flex justify-center md:justify-end">
            <Image
              src={"/Images/Ai_Frame.png"}
              alt="AI"
              width={400}
              height={400}
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-center md:justify-between gap-5 items-center my-5">
          <div className="flex flex-col justify-center items-center gap-3 md:gap-5">
            <ChooseCard
              title={"Scalable & Agile Solutions"}
              content={
                "Our expert team delivers scalable AI solutions that meet the challenges and evolving needs of your business. As your business expands, our implemented agile methodologies easily and quickly adjust to changes. We ensure that you face no obstacles in scaling up your business operations."
              }
            />
            <ChooseCard
              title={"Customer-Centric Approach"}
              content={
                "We believe in developing and implementing AI solutions that are customer-centric. It means we take customer requirements, needs, and preferences into account and deliver tailored solutions accordingly. Our solutions are fully customized with a focus in mind to deliver what your business requires for better business outcomes"
              }
            />
            <ChooseCard
              title={"24/7 Comprehensive Support"}
              content={
                "We strive hard to deliver the best AI solution for your business, right from the initial call to its successful implementation. However, we do not leave you there. We take pride in helping you even after the delivery and implementation. We offer you continuous support and maintenance 24/7."
              }
            />
          </div>
          <div className="flex flex-col justify-center items-center gap-3 md:gap-5">
            <ChooseCard
              title={"Vast Experience  & Track Record"}
              content={
                "We try our best to deliver you the most impactful AI solutions by using the latest platforms, systems, and frameworks that help your business succeed. With our services, your business can take full benefit from AI capabilities. We ensure that our leveraged solutions are competitive and modern."
              }
            />
            <ChooseCard
              title={"Cost-Efficient Solutions"}
              content={
                "We have different pricing options ranging from hourly to fixed, depending on the suitability of your business. We aim to equip businesses with the latest AI solutions development at cost-efficient rates, which can yield higher returns. Our expert team works exclusively to closely understand your business needs and strives to deliver the best solution."
              }
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section7;
