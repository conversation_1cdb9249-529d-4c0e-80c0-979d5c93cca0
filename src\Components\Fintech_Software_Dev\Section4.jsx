import Image from "next/image";
const Container = ({ children, bgColor = "bg-white", className = "" }) => {
  return (
    <div className={`w-full p-3 md:px-[42px] md:py-6 ${bgColor} ${className}`}>
      {children}
    </div>
  );
};

const Bulletpoint = ({ Content, bgColor = "bg-white" }) => {
  return (
    <div
      className={`max-w-lg p-2  flex justify-start items-start ${bgColor} border border-purple-500 rounded-md shadow-sm`}
    >
      <Image src={"/Images/pointer.png"} alt="pointer" className="flex-shrink-0" width={32} height={32} />
      
      <p
          className="text-sm md:text-lg"
          dangerouslySetInnerHTML={{ __html: Content }}
        />
    </div>
  );
};

const Content = ({ title, description }) => {
  return (
    <div>
      <h3 className="text-lg md:text-2xl font-semibold mb-1 md:mb-3">
        {title}
      </h3>
      {description && (
        <p
          className="text-base md:text-xl text-justify"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
    </div>
  );
};
const Section4 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-10 text-center font-semibold mb-6 md:mb-[42px]">
        Valueans Core Services in {" "}
        <span className="text-[#F245A1]">Fintech Development</span>
      </h2>
      <Container
        bgColor="bg-pink-100"
        className="flex flex-col md:flex-row justify-center md:justify-between gap-8 items-center p-4"
      >
        <div className="flex-1">
          <Content
            title={"1. Fintech Application Development"}
            description={
              "In the world today, there is a growing need for mobile financial solutions, thus making the need for fintech apps pivotal to businesses. At Valueans, we create powerful fintech brands and applications that are pleasant and easy to use."
            }
          />
        </div>

        <div className="flex-1">
          <h4 className="text-base md:text-xl font-medium">This includes </h4>
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"Mobile banking apps for safe online transactions."}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"<a href='/Technologies/Progressive_Web_Apps' class='text-[#7716BC] hover:underline'>Progressive web apps</a> with offline capabilities"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Digital wallets that allow effortless payments."}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={
                "Investment and trade apps with up-to-the-minute market information."
              }
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Peer-to-peer lending sites for quick fund transfers."}
              bgColor="bg-purple-200"
            />
          </div>
        </div>
      </Container>
      <Container
        bgColor="bg-purple-100"
        className="flex flex-col md:flex-row justify-center md:justify-between gap-8 items-center p-4 my-3 md:my-6"
      >
        <div className="flex-1">
          <Content
            title={"2. Custom Fintech Software Development Services"}
            description={
              "All businesses deal with different financial issues and therefore expect tailored solutions, thus buying from the shelf may not be a good idea. Our custom fintech software development service is aimed at achieving a customized solution that adapts to the way the business works. "
            }
          />
        </div>

        <div className="flex-1">
          <h4 className="text-base md:text-xl font-medium">This includes </h4>
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={
                "Tailor-made financial management software solutions for companies."
              }
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={
                "Automated Advisors for Financial Planning Tools (new AI powered)."
              }
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={
                "Risk assessment and fraud detection tools with <a href='/ML' class='text-[#7716BC] hover:underline'>machine learning</a>  techniques."
              }
              bgColor="bg-pink-200"
            />
            <Bulletpoint
              Content={"Compliance and regulatory meeting software. "}
              bgColor="bg-pink-200"
            />
          </div>
        </div>
      </Container>
      <Container
        bgColor="bg-pink-100"
        className="flex flex-col md:flex-row justify-center md:justify-between gap-8 items-center p-4"
      >
        <div className="flex-1">
          <Content
            title={"3. Fintech Software Development Services"}
            description={
              "With our broad range of fintech development services, every business is guaranteed to obtain the latest technologies available in the financial market. Valueans believes in developing safe, scalable, and efficient solutions for financial systems that improve business performance. "
            }
          />
        </div>

        <div className="flex-1">
          <h4 className="text-base md:text-xl font-medium">
            Below is a detailed breakdown of our service.{" "}
          </h4>
          <div className="flex flex-col  gap-3 md:gap-4 my-2 md:my-4">
            <Bulletpoint
              Content={"Accessibility Cloud Financial Services"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Integration of Blockchain Technology"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={"Provision of Payment Processing Gateways"}
              bgColor="bg-purple-200"
            />
            <Bulletpoint
              Content={
                "Development of Smart Contracts for Financial Transactions "
              }
              bgColor="bg-purple-200"
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Section4;
