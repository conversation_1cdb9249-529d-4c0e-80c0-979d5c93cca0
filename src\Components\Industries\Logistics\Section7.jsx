import React from "react";

const Section7 = ({ heading, spanheading, cardData }) => {
  return (
    <div className="bg-[#1F62EA26] py-5">
      <div className="container mb-10 md:mb-24">
        <div className="my-4">
          <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
            {heading} <span className="text-[#F245A1]">{spanheading}</span>
          </h2>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2  gap-8 mt-6">
          {cardData.map((card, index) => (
            <div
              key={index}
              className="border border-[#7716BC] rounded-lg bg-white p-4 w-[100%] mx-auto"
            >
              <h4 className="text-base  my-2 font-semibold">{card.title}</h4>

              <p
                className="text-sm mb-2 text-justify"
                dangerouslySetInnerHTML={{ __html: card.description }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section7;
