import React from "react";
import ServiceCard_2 from "../Card/ServiceCard_2";

const Section5 = () => {
  const cardData = [
    {
      title: "Boost your Business Efficiency",
      content:
        "Our provided AI solutions make your day-to-day tasks easier and quicker, significantly boosting efficiency across various business operations. AI helps you process copious amounts of information with greater accuracy and efficiency.",
    },
    {
      title: "Helps you make Informed Decisions",
      content:
        "Our enterprise AI solutions provide comprehensive data sets and insights into your business operations. Analyzing those datasets will help you make more accurate predictive analyses and smarter decisions for your business.",
    },
    {
      title: "Lower Down your Business Costs",
      content:
        "Our implemented AI business solutions help you cut down on added expenses related to manual labor and reduce the chances of human error. This results in financial savings for your business, helping you get better ROI.",
    },
    {
      title: "Mitigates Potential Risks",
      content:
        "As compared to traditional methods of mitigating potential business risks, AI can help you identify risks more efficiently and accurately. With our AI solutions development services, you can proactively manage these threats and vulnerabilities.",
    },
    {
      title: "Efficient Data Management",
      content:
        "With Valueans AI business solutions, you can experience better data management by interpreting complex data structures into actionable outcomes. You can use these outcomes to make better business decisions and optimize your progress.",
    },
    {
      title: "Portfolio Management",
      content:
        "With our efficient AI business solutions, you can refine your portfolio allocation, automate trades, and make better investment strategies. It helps you yield better business outcomes with enhanced performance. ",
    },
  ];

  return (
    <div className="container my-10 md:my-24">
      <h2 className="text-2xl md:text-3xl text-center font-semibold  ">
        How Valueans{" "}
        <span className="text-[#F245A1]">
          AI Business <br /> Solutions
        </span>{" "}
        Help You
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 p-6 ">
        {cardData.map((card, index) => (
          <ServiceCard_2
            key={index}
            title={card.title}
            content={card.content}
          />
        ))}
      </div>
    </div>
  );
};

export default Section5;
