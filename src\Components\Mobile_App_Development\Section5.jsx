import ServiceCount from "../Services/ServiceCount_2";

const Card = ({ title, description }) => {
  return (
    <div className="block max-w-xl p-4 border border-purple-600 bg-white shadow-sm rounded-md">
      <h3 className="text-base font-semibold">{title}</h3>
      <p
        className="text-sm "
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

const Section5 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 py-3 md:py-8">
      <div className="container">
        <h2 className="text-2xl md:text-3xl md:leading-8 text-center font-semibold mb-1 md:mb-2">
          Our <span className="text-[#7716BC]">Mobile App Design</span> Services
          process
        </h2>

        <div className="flex flex-col md:flex-row justify-center items-center gap-4 mt-4 md:mt-8 mb-4">
          <div className="flex flex-col md:flex-row  justify-center items-center gap-4">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>1</ServiceCount>
              <Card
                title={"Ideation"}
                description={
                  "We start by carrying out product research and discovery."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              {" "}
              <ServiceCount>2</ServiceCount>
              <Card
                title={"Design"}
                description={
                  "Then we will work on the <a href='/ui-ux' class='text-[#7716BC] hover:underline'>  UX and UI</a> design. We may also create branding if required."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>3</ServiceCount>
              <Card
                title={"Development"}
                description={
                  "In the third step, we develop a mobile application that meets your needs."
                }
              />
            </div>
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 mt-4 md:mt-8 mb-4">
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>4</ServiceCount>
            <Card
              title={"Quality Assurance"}
              description={
                "After that, we ensure that the application is running smoothly by thoroughly testing it."
              }
            />
          </div>
          <div className="flex justify-center items-center gap-2">
            <ServiceCount>5</ServiceCount>
            <Card
              title={"Maintenance & Support"}
              description={
                "Together with your tech team, we are here to co-manage your app."
              }
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section5;
