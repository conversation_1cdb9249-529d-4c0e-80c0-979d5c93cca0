import React from 'react'
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: "Secure FinTech Loan Application Platform | Valueans",
  description: "FinTech web app offering a fast, secure loan application process. It features profile creation, transaction history, and data protection for financial management.",
  alternates: {
    canonical: "https://valueans.com/portfolio/gtr-loan",
  },
  openGraph: {
    title: "Secure FinTech Loan Application Platform | Valueans",
    description: "FinTech web app offering a fast, secure loan application process. It features profile creation, transaction history, and data protection for financial management.",
    url: "https://valueans.com/portfolio/gtr-loan",
    type: "website",
  },
};

const page = () => {
 const images = ["/Images/GTR2.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/GTR-bg.png"} />
      <Section2
        image={"/Images/GTR1.png"}
        heading={"GTR LOAN"}
        paragraph1={
          "Another FinTech product created by Valueans is GTR LOAN, which provides a simple and rapid application procedure for borrowing money. Users can get money using this platform without having to go through any other steps outside of it. Because money was involved in the development of this platform, making it secure was the major challenge."
        }
        paragraph2={
          "When developing web solutions like this one, we constantly want to use the latest technological stacks, especially for FinTech. We used React technology to develop an intuitive front end and Django to create a seamless backend."
        }
      />
      <Section4
        paragraph1={
          "We developed a user-friendly interface that makes it easy for users to create profiles by adding their personal information. Moreover, users can access their transaction history and keep a record of their financial data. With our expertise, we successfully delivered this project to our client and met all the needs to make this solution a success."
        }
      />
      <Section5 images={images} />
    </div>
  )
}

export default page