import React from "react";

const Section2 = () => {
  return (
    <section className="container flex flex-col md:flex-row justify-between items-center  my-10 md:my-24">
      <div className="p-1 md:p-10 flex-1">
        <p className="text-[#F245A1] text-base md:text-xl text-justify">
          Are you making the most of your data? With so much information coming at you, it’s easy to feel overwhelmed. That’s where we come in. At Valueans, we help turn your data into simple, clear insights that make a real difference. Whether it’s spotting trends, improving efficiency, or making confident decisions, our business intelligence and analytics services make it easier for you to implement those effectively.
        </p>
      </div>
      <div className="p-1 md:p-10 border-t-2 md:border-t-0 md:border-l-4 border-[#F245A1] flex-1">
        <p className=" text-base md:text-lg text-justify">
          Having trouble keeping up with fast-changing markets? BI can help you stay ahead by identifying trends early. Need to boost productivity? We’ll help you streamline your processes and get more done. Ready to make your data your biggest advantage? Read more to know about how our BI tools can help you succeed in today’s fast-paced world.
        </p>
      </div>
    </section>
  );
};

export default Section2;
