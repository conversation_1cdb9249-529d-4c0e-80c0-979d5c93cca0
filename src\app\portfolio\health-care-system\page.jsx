import React from 'react'
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: "HIPAA-Compliant Hospital Management Software | Valueans",
  description: "A secure, HIPAA-compliant Health Care System. Enables hospitals to manage patient records, track treatment history, and streamline operations while ensuring data privacy and fast performance.",
  alternates: {
    canonical: "https://valueans.com/portfolio/health-care-system",
  },
  openGraph: {
    title: "HIPAA-Compliant Hospital Management Software | Valueans",
    description: "A secure, HIPAA-compliant Health Care System. Enables hospitals to manage patient records, track treatment history, and streamline operations while ensuring data privacy and fast performance.",
    url: "https://valueans.com/portfolio/health-care-system",
    type: "website",
  },
};

const page = () => {
 const images = ["/Images/HC3.png", "/Images/HC4.png", "/Images/HC5.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/HealthCare-bg2.webp"} />
      <Section2
        image={"/Images/HC2.png"}
        heading={"Health care system"}
        paragraph1={
          "The Health Care System is one of our most successful projects that has helped a hospital improve its management and operations. Due to the nature of the healthcare industry, we had to be very particular about which technology stack we use so that there are no errors in its processing. "
        }
        paragraph2={
          "We chose Django and VueJS to develop this system. With these technologies, we have been able to make this system HIPAA-compliant. This system required a very fast processing system, so we chose Django for the backend development. To make it responsive and user-friendly, we chose Vue.js for front-end development."
        }
      />
    
      <Section4
        paragraph1={
          "Another challenge was to keep the data secure to protect the privacy of the patients. Our Health Care System has helped hospitals to easily maintain patient data, retrieve past records of the patients, and check what treatments they have received previously. "
        }
        paragraph2={
          "The data stored in our Health Care System makes it easier for doctors to provide the most suitable treatments to patients, hence improving patient care."
        }
      />
      <Section5 images={images} />
    </div>
  )
}

export default page