
import Box from "@mui/material/Box"
import EstimateHeader from "@/Components/EstimateHeader"

export const metadata = {
    title: "We are IT support specialists with comprehensive plans for your assistance",
    description: "IT support specialists that cater for all your business needs. From basic to advanced technical support we have a plan that fits your requirements.",
    keywords: [
      "Valueans Support Services",
      "Customized Technical Support Plans",
      "Professional Technical Assistance",
      "Support Plans for Every Business Size",
      "Valueans Customer Care",
    ],
    alternates: {
      canonical: `https://valueans.com/support`,
    },
    openGraph: {
      title: "We are IT support specialists with comprehensive plans for your assistance",
      description: "IT support specialists that cater for all your business needs. From basic to advanced technical support we have a plan that fits your requirements.",
      url: "https://valueans.com/support",
      type: "website",
    },
    og: {
      title: "Find the Right Support Plan at Valueans",
      description: "Valueans offers a range of support plans tailored to your business's unique requirements. Discover our basic, advanced, and premium support options today.",
      type: "website",
      url: "https://valueans.com/support",
      image: "https://valueans.com/Images/logo.png"
    }
  };

const Page = () => {
  return (
    <>
      <EstimateHeader title="Comprehensive Support Plans Tailored for You" description="Choose from our three distinct support plans at Valueans, each designed to provide the level of assistance and expertise your business needs. Whether you require basic guidance, advanced technical support, or premium, all-encompassing services, we have a plan that fits your requirements perfectly." propVal={false}/>
      <Box mt={6} padding="40px">
        <stripe-pricing-table pricing-table-id={process.env.STRIPE_SUPPORT_TABLE_ID}
            publishable-key={process.env.STRIPE_PUBLISH_KEY}>
        </stripe-pricing-table>
      </Box>
      </>
  )
}

export default Page