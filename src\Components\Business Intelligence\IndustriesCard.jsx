import Image from "next/image";
import React from "react";

const IndustriesCard = ({ imgSrc, altText, title, description }) => {
  return (
    <div className="block w-full md:max-w-sm h-auto md:h-[200px] p-4 bg-white border border-purple-800 rounded-md shadow">
      <div className="flex items-center gap-3">
        <Image src={imgSrc} alt={altText} width={32} height={32} />
        <h3 className="text-lg md:text-xl font-semibold">{title}</h3>
      </div>
      <p
        className="text-base md:text-lg "
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

export default IndustriesCard;
