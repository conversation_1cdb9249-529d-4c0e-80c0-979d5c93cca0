import React from "react";
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: "SaaS E-Commerce Platform for Online Medicine Sales | Valueans",
  description: "A secure e-commerce SaaS platform for online medicine sales. It connects licensed pharmacies with buyers, featuring real-time inventory management, secure data handling, and seamless order processing.",

  alternates: {
    canonical: "https://valueans.com/portfolio/ekoj",
  }};
const page = () => {
  const images = ["/Images/Ekoj2.png", "/Images/Ekoj3.png", "/Images/Ekoj4.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/Ekoj-bg.png"} />
      <Section2
        image={"/Images/Ekoj1.png"}
        heading={"EKOJ"}
        paragraph1={
          "Our client required us to develop an e-commerce SaaS product for selling medicines online. The major purpose of this SaaS product is to make medicine easily available to everyone. EKOJ has licensed pharmacies under its panel from which buyers can easily buy medicines, and we had to develop a solution that can connect them."
        }
        paragraph2={
          "The major concern in developing this solution was to overcome the challenge of efficiently managing the medicine inventory. In the medical field, it's extremely critical to maintain the inventory, which we made easy with EKOJ. Our solution has helped the business to process orders effectively and efficiently."
        }
      />
     
      <Section4
        paragraph1={
          "To make these processes smooth and seamless, we chose React for frontend development and Django for backend development. The latest technology stack helped EKOJ deliver flawless service to people looking for medicines. It has a user-friendly interface that runs seamlessly across multiple devices.  "
        }
        paragraph2={
          "While developing EKOJ, we took care of the confidentiality and security of the user data as well. This project has made buying medicines online easier than ever and at the same time, it has made selling efficient for the business."
        }
      />
      <Section5 images={images} />
    </div>
  );
};

export default page;
