import ServiceCount from "../Services/ServiceCount_2";

const Card = ({ title, description }) => {
  return (
    <div className="block max-w-xl p-4 border border-purple-600 bg-white shadow-sm rounded-md">
      <h3 className="text-base font-semibold">{title}</h3>
      {description && (
        <p
          className="text-sm "
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
    </div>
  );
};

const Section6 = () => {
  return (
    <section className="bg-pink-100  py-3 md:py-8 mb-10 md:mb-24">
      <div className="container">
        <h2 className="text-2xl md:text-3xl md:leading-[40px] text-center font-semibold mb-1 md:mb-2">
          Our Proven <span className="text-[#7716BC]">Web Development</span>{" "}
          Process
        </h2>
        <p className="md:w-[75%] md:mx-auto text-base md:text-xl text-center">
         Because of the numerous projects we've completed, we understand what it takes to carry out an efficient and effective web-based software development process.
        </p>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 mt-8">
          <div className="flex flex-col justify-center items-center gap-4">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>1</ServiceCount>
              <Card
                title={"Consult Your Idea"}
                description={
                  "Professional advice is the first step in developing an effective online solution. Our web development specialists will assist you in determining your needs, establishing objectives, and formulating a plan that will guarantee the success of your project from beginning to end. Our professionals compile your needs and suggestions into a comprehensive brief. We convert these into early-stage prototypes and technical papers."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              {" "} 
              <ServiceCount>3</ServiceCount>
              <Card
                title={"UX/UI Design and IA"}
                description={
                  "Here, by creating user-centric and user-friendly interfaces, we integrate distinctive branding components that flawlessly capture your company's personality. To maximize user experience and guarantee consistent replies across platforms and devices, we take a <a href='/mobile-app-development' class='text-[#7716BC] hover:underline'>mobile-first strategy.</a>"
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>5</ServiceCount>
              <Card
                title={"Testing"}
                description={
                  "Strict testing techniques enable us to quickly identify and fix technical issues. By submitting the final sprint demo for customer approval and doing regression testing to confirm that the authorized components are operating as intended, we guarantee high-quality releases."
                }
              />
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-4">
            <div className="flex justify-center items-center gap-2">
              {" "}
              <ServiceCount>2</ServiceCount>
              <Card 
                title={"Planning & Documentation"}
                description={
                  "Creating a project strategy and outlining the deliverables and milestones is the first step in our website development roadmap. Furthermore, we facilitate effective and simple cross-team cooperation by documenting every step of the web development process."
                } 
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>4</ServiceCount>
              <Card
                title={"Development"}
                description={
                  "Building a controllable backend for reliable, superior apps is what makes our custom development services in the USA so successful. We schedule sprint reviews every two to three weeks to provide transparency and alignment for performance efficiency."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>6</ServiceCount>
              <Card
                title={"Maintenance"}
                description={
                  "We can preserve the web application's functionality and appearance by keeping an eye on it after it has been deployed. Additionally, our maintenance and support services are made to protect against such weaknesses. We also continue to update the system on a regular basis."
                }
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section6;
