import Image from "next/image";
import React from "react";
import { <PERSON>P<PERSON>, FiMail, FiMapPin } from "react-icons/fi";
import Link from "next/link";

const Footer = () => {
  return (
    <>
      <section className="bg-gradient-to-r from-pink-100 via-purple-100 to-blue-100">
        <div className="max-w-[85%] md:w-full mx-auto pt-11 flex flex-col-reverse md:flex-row justify-between  mb-4">
          <div>
            <h2 className="text-[#232536] text-3xl leading-[48px] font-semibold">
              Let's make
              <br /> something special
            </h2>
            <div className="my-4">
              <span className="text-[#F245A1] text-xl md:text-2xl font-medium py-2 md:py-4">
                Let's talk! 🤙
              </span>
            </div>

            <div className="flex flex-col justify-center items-start gap-4">
              <div className="flex justify-center items-center gap-1">
                <div className="rounded-full bg-[#7716BC] text-white  max-w-fit  p-2">
                  <FiPhone />
                </div>
                <p className="text-lg leading-[27px] font-medium text-[#232536]">
                  +****************
                </p>
              </div>
              <div className="flex justify-center items-center gap-1">
                <div className="rounded-full bg-[#7716BC] text-white  max-w-fit p-2">
                  <FiMail />
                </div>
                <p className="text-lg leading-[27px] font-medium text-[#232536]">
                  {" "}
                  <EMAIL>
                </p>
              </div>
              <div className="flex  justify-center items-center gap-1">
                <div className="rounded-full bg-[#7716BC] text-white  max-w-fit  p-2">
                  <FiMapPin />
                </div>
                <p className="text-sm md:text-lg font-medium text-[#232536]">
                  10 Raker CT Hillsborough , New Jersey 08844 USA
                </p>
              </div>
              <div className="text-base font-medium text-[#232536] flex gap-2 justify-center items-center">
                <div>
                  <Image
                    src="/Images/line-shape.png"
                    alt="line"
                    height={10}
                    width={50}
                  />
                </div>
                <span className="text-base font-medium text-[#232536]">
                  Contact Us
                </span>
                <div>
                  <Image
                    src="/Images/Arrow-icon.png"
                    alt="arrow"
                    width={20}
                    height={2}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-between md:justify-center gap-9 w-[90%] md:w-auto mx-auto md:mx-0">
            <ul>
              <li className="text-base md:text-xl font-bold">Resources</li>
              <li className="text-sm md:text-lg mt-1">
                <Link href="/" className="hover:text-[#F245A1]">
                  Home
                </Link>
              </li>
              <li className="text-sm md:text-lg mt-1">
                <Link href="/portfolio" className="hover:text-[#F245A1]">
                  Portfolio
                </Link>
              </li>
              {/* <li className="text-sm md:text-lg mt-1">
                <Link href="#" className="hover:text-[#F245A1]">
                  Services
                </Link>
              </li> */}
              {/* <li className="text-sm md:text-lg mt-1">
                <Link href="#" className="hover:text-[#F245A1]">
                  Career
                </Link>
              </li> */}
              <li className="text-sm md:text-lg mt-1">
                <Link href="/blog" className="hover:text-[#F245A1]">
                  Blog
                </Link>
              </li>
            </ul>
            <ul>
              <li className="text-base md:text-xl font-bold">Quick Links</li>
              <li className="text-sm md:text-lg mt-1">
                <Link href="/maintenance" className="hover:text-[#F245A1]">
                  Maintenance
                </Link>
              </li>
              <li className="text-sm md:text-lg mt-1">
                <Link href="/support" className="hover:text-[#F245A1]">
                  Support
                </Link>
              </li>
              <li className="text-sm md:text-lg mt-1">
                <Link href="/hosting" className="hover:text-[#F245A1]">
                  Hosting
                </Link>
              </li>
            </ul>
            <ul>
              <li className="text-xl font-bold">Helpful Resources</li>
              {/* <li className="text-sm md:text-lg mt-1">
                <Link href="#" className="hover:text-[#F245A1]">
                  Testimonial
                </Link>
              </li> */}
              <li className="text-sm md:text-lg mt-1">
                <Link href="/privacy-policy" className="hover:text-[#F245A1]">
                  Privacy Policy
                </Link>
              </li>
              <li className="text-sm md:text-lg mt-1">
                <Link href="/terms-and-conditions" className="hover:text-[#F245A1]">
                  Terms of use
                </Link>
              </li>
              <li className="text-sm md:text-lg mt-1">
                <Link href="/contact" className="hover:text-[#F245A1]">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="text-center  bg-[#F245A1]">
          <h2 className="text-white py-4">©2024 Valueans</h2>
        </div>
      </section>
    </>
  );
};

export default Footer;
