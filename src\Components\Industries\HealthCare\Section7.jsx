import React from "react";
import PinkTopCard from "@/Components/PWA_development/PinkTopCard";

const Section7 = ({
  heading,
  PinkTopCardData,
  Cardheight,
  paragraph,
  spanHeading,
  gridClass,
  paragraph2,
  cardHeaderHeight
}) => {
  return (
    <div className="container mb-10 md:my-24 ">
      <div className="mb-7">
        <h2 className="text-xl md:text-3xl my-10 md:leading-[40px] md:w-[50%] mb-4 mx-auto font-semibold text-center">
          {heading} <span className="text-[#7716BC]">{spanHeading}</span>
        </h2>
        <p className="text-base md:text-lg text-center">{paragraph}</p>
      </div>
      <div className={`max-w-fit mx-auto grid ${gridClass ? gridClass : "md:grid-cols-3"}   gap-8 mt-6 md:mt-[0px]`}>
        {PinkTopCardData.map((card, index) => (
          <PinkTopCard
            key={index}
            title={card.title}
            description={card.description}
            PinkTopCardheight={Cardheight}
            listData={card.listData}
            cardHeaderHeight={cardHeaderHeight}
          />
        ))}
      </div>
      {paragraph2 && (
        <div className="bg-[#F245A1] md:w-[70%] p-5 flex justify-center mx-auto mt-10 rounded-lg">
          <p className="text-white text-center text-base md:text-lg">
            {paragraph2}
          </p>
        </div>
      )}
      
    </div>
  );
};

export default Section7;
