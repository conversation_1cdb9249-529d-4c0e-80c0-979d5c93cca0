import React from "react";
import Faq from "@/Components/Faq/Faq";
import Section1 from "@/Components/AI/Section1";
import Section2 from "@/Components/AI/Section2";
import Section3 from "@/Components/Mobile_App_Development/Section3";
import Section4 from "@/Components/Mobile_App_Development/Section4";
import Section5 from "@/Components/Mobile_App_Development/Section5";
import Section6 from "@/Components/Mobile_App_Development/Section6";
import Section7 from "@/Components/Mobile_App_Development/Section7";
import Section8 from "@/Components/Mobile_App_Development/Section8";
export const metadata = {
  title: "Enterprise mobile app development services & integrations",
  description: "We provide enterprise mobile app development services with captivating UX, across several platforms. Discover advanced technologies to pair with your app.",

  alternates: {
    canonical: "https://valueans.com/mobile-app-development",
  }};
const page = () => {
  const accordionData = [
    {
      title: "What is a mobile app development service?",
      content:
        "Mobile application development is the process of producing software for smartphones, tablets, and digital assistants, most typically for the Android and iOS operating systems. The program can be accessed via a mobile web browser, downloaded from a mobile app store, or preloaded on the device.",
    },
    {
      title: "Is working with a mobile app development business worthwhile?",
      content:
        "It is worthwhile working with a mobile app development company. They have the know-how to create top-notch software while lowering risks and saving time. In addition to giving you a gold-mine experience, agencies also provide a certain amount of assistance to keep your app competitive and up to date. ",
    },
    {
      title: "How much does it cost to build a mobile app from scratch?",
      content:
        "Creating a basic app typically costs between $5,000 and $50,000. Basic user features including a dashboard, user profile functionality, login system, and minimal management are included in a simple app. A medium-complexity app may cost between $50,000 and $120,000. ",
    },
    {
      title: "Is creating a website or an app more affordable?",
      content:
        "Developing a website is typically less expensive than developing a mobile app. A simple app starts at $20,000, whereas a basic website may cost $5,000 to $25,000. The decision, however, is based on your target market and particular company requirements. ",
    },
    {
      title:
        "Should I start by making an app or website?",
      content:
        "Building a website initially is preferable to investing a sizable portion of your resources in creating a mobile app, unless you are a start-up with a mobile focus. Alternatively, you might strive to improve your website's usability or even spend more money to improve its search engine ranking.",
    },
    {
      title: "Is creating an app from a website difficult?",
      content:
        "Custom app creation is an option, but it can cost a lot of money, require technical expertise, and take months to complete. An online mobile builder that requires no coding is an alternative. This application offers an easy-to-use drag-and-drop interface for creating apps quickly, making it ideal for anyone without coding skills. ",
    },
    {
      title: "How long does a mobile project typically take? If I need to release an app right away, is there any way to expedite the development process?",
      content:
        "Mobile app development can take anywhere from three to ten months, plus two to four months for a complementary web app, depending on the tech stack and scope. Valueans prioritize features, concentrating on the most important ones first, in order to shorten time-to-market. In only two to three months, we release an MVP app and carry on iteratively improving it. ",
    },
  ];
  return (
    <>
      <Section1
        backgroundImage={"/Images/mobile-development-bg.jpeg"}
        heading={"Enterprise Mobile App Development Services"}
        bannerText={
          "We Develop Aesthetic Mobile App Solutions with Captivating UX Across Several Platforms."
        }
      />
      <Section2
        lefttext={"Custom Mobile Application Development Services"}
        righttext={
          "Valueans creates reliable, scalable apps that can grow with your company. To differentiate ourselves in a crowded industry, we develop cross-platform, native, and progressive web apps with dependable performance, user-centered design, and a USP."
        }
      />
      <Section3 />
      <Section4 />
      <Section8/>
      <Section5 />
      <Section6 />
      <Section7 />
      <Faq content={accordionData}/>
    </>
  );
};

export default page;
