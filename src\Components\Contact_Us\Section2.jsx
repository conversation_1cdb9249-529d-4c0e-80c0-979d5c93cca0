'use client'

import React, { useState } from 'react'
import <PERSON>ton from '../Buttons/Button'
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa";
import Link from "next/link";
import Toast from '../ui/Toast';
import useToast from '../../hooks/useToast';
import useSocialLinks from "@/hooks/useSocialLinks";

const Section2 = () => {
  // State to handle form data
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone_number: '',
    message: ''
  });
  const { socialLinks, loading, error } = useSocialLinks();

  // State for form submission status
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Toast notification hook
  const { toast, showSuccess, showError, hideToast } = useToast();

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
console.log(formData)
    try {
      const response = await fetch('https://api.valueans.com/api/contact-us/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        showSuccess("Thank you for your submission! We will contact you shortly.");
        setFormData({
          name: '',
          email: '',
          phone_number: '',
          message: ''
        });
      } else {
        showError("Something went wrong. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting the form:", error);
      showError("An error occurred. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="w-full py-8 md:py-16">
      <div className="w-[90%] mx-auto">
        <div className="flex flex-col md:flex-row gap-8 md:gap-8">
          {/* Address, Hours, Contact Us */}
          <div className="w-full md:w-[30%] px-4 md:px-8 order-2 md:order-1 flex flex-col gap-6">
            <div className="rounded-lg">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-[#7716BC] rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Address</h3>
              </div>
              <div className="text-gray-600 pl-11">
                <p>10 Raker CT Hillsborough,</p>
                <p>New Jersey 08844 USA</p>
              </div>
            </div>

            {/* Working Hours */}
            <div className="rounded-lg">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-[#7716BC] rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Working Hours</h3>
              </div>
              <div className="text-gray-600 pl-11">
                <p>Monday To Friday</p>
                <p>9:00 AM to 5:00 PM EST</p>
                <p className="mt-2 text-sm">Our Support Team is available</p>
              </div>
            </div>

            {/* Contact Us */}
            <div className="rounded-lg">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-[#7716BC] rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Contact Us</h3>
              </div>
              <div className="text-gray-600 pl-11">
                <p>+1 (302) 217-3058</p>
                <p><EMAIL></p>
              </div>
            </div>

            {socialLinks && (
              <div className="flex gap-6 pl-11 mt-2">
                <Link href={socialLinks.facebook} target="_blank">
                  <FaFacebook className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                </Link>
                <Link href={socialLinks.instagram} target="_blank">
                  <FaInstagram className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                </Link>
                <Link href={socialLinks.linkedIn} target="_blank">
                  <FaLinkedin className="w-4 md:w-6 h-4 md:h-6 hover:scale-110 transition-transform" />
                </Link>
              </div>
            )}
          </div>

          {/* Form Section */}
          <div className="w-full md:w-[70%] px-4 md:px-8 order-1 md:order-2">
            <form onSubmit={handleSubmit} className="flex flex-col gap-4 bg-[#EE75CD1A] p-4 md:p-8 rounded-md">
              <h2 className="text-2xl md:text-3xl font-bold text-[#7716BC] mb-4">
                Get In Touch
              </h2>
              <p className="text-gray-600 text-base md:text-lg">
                Ready to start your project? Contact us today and let's discuss how we can help bring your vision to life.
              </p>
              <input
                type="text"
                className="p-3 rounded-sm border border-gray-300 focus:outline-none focus:border-[#F245A1]"
                name="name"
                id="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Name"
                required
              />
              <input
                type="email"
                className="p-3 rounded-sm border border-gray-300 focus:outline-none focus:border-[#F245A1]"
                name="email"
                id="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Email"
                required
              />
              <input
                type="tel"
                className="p-3 rounded-sm border border-gray-300 focus:outline-none focus:border-[#F245A1]"
                name="phone_number"
                id="phone_number"
                value={formData.phone_number}
                onChange={handleInputChange}
                placeholder="Phone Number"
                required
              />
              <textarea
                name="message"
                id="message"
                className="p-3 rounded-sm border border-gray-300 focus:outline-none focus:border-[#F245A1] mb-4"
                rows={4}
                value={formData.message}
                onChange={handleInputChange}
                placeholder="Message"
                required
              ></textarea>
              <Button bgColor="bg-[#F245A1]" hoverColor="opacity-90" disabled={isSubmitting}>
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </Button>
            </form>
          </div>
        </div>
      </div>

      {/* Toast Notification */}
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </section>
  );
}

export default Section2;
