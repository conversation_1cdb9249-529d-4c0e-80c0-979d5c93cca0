#!/usr/bin/env node

/**
 * SEO Verification Script for Valueans Website
 * This script verifies that all SEO improvements are working correctly
 */

const fs = require('fs');
const path = require('path');

const EXPECTED_DOMAIN = 'https://valueans.com';

console.log('🔍 Verifying SEO Configuration...\n');

// Check robots.txt
function checkRobotsTxt() {
  console.log('📋 Checking robots.txt...');
  try {
    const robotsPath = path.join(process.cwd(), 'public', 'robots.txt');
    const robotsContent = fs.readFileSync(robotsPath, 'utf8');
    
    if (robotsContent.includes(EXPECTED_DOMAIN)) {
      console.log('✅ robots.txt points to correct domain');
    } else {
      console.log('❌ robots.txt domain mismatch');
      console.log('   Expected:', EXPECTED_DOMAIN);
      console.log('   Found:', robotsContent);
    }
  } catch (error) {
    console.log('❌ robots.txt not found or error reading file');
  }
  console.log('');
}

// Check sitemap.xml
function checkSitemapXml() {
  console.log('🗺️  Checking sitemap.xml...');
  try {
    const sitemapPath = path.join(process.cwd(), 'public', 'sitemap.xml');
    const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
    
    const urlMatches = sitemapContent.match(/<loc>([^<]+)<\/loc>/g);
    if (urlMatches) {
      const correctUrls = urlMatches.filter(url => url.includes(EXPECTED_DOMAIN));
      const totalUrls = urlMatches.length;
      
      console.log(`✅ Found ${totalUrls} URLs in sitemap`);
      console.log(`✅ ${correctUrls.length}/${totalUrls} URLs use correct domain`);
      
      if (correctUrls.length !== totalUrls) {
        console.log('❌ Some URLs still use incorrect domain');
      }
    } else {
      console.log('❌ No URLs found in sitemap');
    }
  } catch (error) {
    console.log('❌ sitemap.xml not found or error reading file');
  }
  console.log('');
}

// Check layout.js metadata
function checkLayoutMetadata() {
  console.log('📄 Checking layout.js metadata...');
  try {
    const layoutPath = path.join(process.cwd(), 'src', 'app', 'layout.js');
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');
    
    if (layoutContent.includes('defaultMetadata')) {
      console.log('✅ Layout uses centralized SEO configuration');
    } else {
      console.log('❌ Layout not using centralized SEO configuration');
    }
    
    if (layoutContent.includes('organizationStructuredData')) {
      console.log('✅ Structured data for Organization added');
    } else {
      console.log('❌ Organization structured data missing');
    }
    
    if (layoutContent.includes('websiteStructuredData')) {
      console.log('✅ Structured data for Website added');
    } else {
      console.log('❌ Website structured data missing');
    }
  } catch (error) {
    console.log('❌ layout.js not found or error reading file');
  }
  console.log('');
}

// Check SEO config file
function checkSeoConfig() {
  console.log('⚙️  Checking SEO configuration...');
  try {
    const seoConfigPath = path.join(process.cwd(), 'src', 'lib', 'seo-config.js');
    const seoConfigContent = fs.readFileSync(seoConfigPath, 'utf8');
    
    if (seoConfigContent.includes(EXPECTED_DOMAIN)) {
      console.log('✅ SEO config uses correct domain');
    } else {
      console.log('❌ SEO config domain mismatch');
    }
    
    if (seoConfigContent.includes('organizationStructuredData')) {
      console.log('✅ Organization structured data defined');
    }
    
    if (seoConfigContent.includes('websiteStructuredData')) {
      console.log('✅ Website structured data defined');
    }
    
    if (seoConfigContent.includes('generatePageMetadata')) {
      console.log('✅ Page metadata generator available');
    }
  } catch (error) {
    console.log('❌ SEO config file not found or error reading file');
  }
  console.log('');
}

// Check dynamic sitemap
function checkDynamicSitemap() {
  console.log('🔄 Checking dynamic sitemap...');
  try {
    const sitemapJsPath = path.join(process.cwd(), 'src', 'app', 'sitemap.js');
    const sitemapJsContent = fs.readFileSync(sitemapJsPath, 'utf8');
    
    if (sitemapJsContent.includes(EXPECTED_DOMAIN)) {
      console.log('✅ Dynamic sitemap uses correct domain');
    } else {
      console.log('❌ Dynamic sitemap domain mismatch');
    }
    
    if (sitemapJsContent.includes('export default function sitemap')) {
      console.log('✅ Dynamic sitemap properly exported');
    }
  } catch (error) {
    console.log('❌ Dynamic sitemap not found or error reading file');
  }
  console.log('');
}

// Check dynamic robots
function checkDynamicRobots() {
  console.log('🤖 Checking dynamic robots...');
  try {
    const robotsJsPath = path.join(process.cwd(), 'src', 'app', 'robots.js');
    const robotsJsContent = fs.readFileSync(robotsJsPath, 'utf8');

    if (robotsJsContent.includes(EXPECTED_DOMAIN)) {
      console.log('✅ Dynamic robots uses correct domain');
    } else {
      console.log('❌ Dynamic robots domain mismatch');
    }

    if (robotsJsContent.includes('export default function robots')) {
      console.log('✅ Dynamic robots properly exported');
    }
  } catch (error) {
    console.log('❌ Dynamic robots not found or error reading file');
  }
  console.log('');
}

// Check canonical URL implementation
function checkCanonicalUrls() {
  console.log('🔗 Checking canonical URL implementation...');

  // Check if old CanonicalHead component exists (should be removed)
  try {
    const canonicalHeadPath = path.join(process.cwd(), 'src', 'Components', 'CanonicalHead.jsx');
    fs.readFileSync(canonicalHeadPath, 'utf8');
    console.log('❌ Old CanonicalHead component still exists (should be removed)');
  } catch (error) {
    console.log('✅ Old CanonicalHead component properly removed');
  }

  // Check if SEO config has canonical URL support
  try {
    const seoConfigPath = path.join(process.cwd(), 'src', 'lib', 'seo-config.js');
    const seoConfigContent = fs.readFileSync(seoConfigPath, 'utf8');

    if (seoConfigContent.includes('alternates:') && seoConfigContent.includes('canonical:')) {
      console.log('✅ SEO config includes canonical URL support');
    } else {
      console.log('❌ SEO config missing canonical URL support');
    }

    if (seoConfigContent.includes('generatePageMetadata') && seoConfigContent.includes('canonical')) {
      console.log('✅ Page metadata generator includes canonical URLs');
    } else {
      console.log('❌ Page metadata generator missing canonical URL support');
    }
  } catch (error) {
    console.log('❌ Error checking SEO config for canonical URLs');
  }

  // Check layout.js for CanonicalHead import (should be removed)
  try {
    const layoutPath = path.join(process.cwd(), 'src', 'app', 'layout.js');
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');

    if (layoutContent.includes('CanonicalHead')) {
      console.log('❌ Layout still imports CanonicalHead component');
    } else {
      console.log('✅ Layout properly cleaned of CanonicalHead references');
    }
  } catch (error) {
    console.log('❌ Error checking layout.js');
  }

  console.log('');
}

// Run all checks
function runAllChecks() {
  console.log(`🎯 Target Domain: ${EXPECTED_DOMAIN}\n`);

  checkRobotsTxt();
  checkSitemapXml();
  checkLayoutMetadata();
  checkSeoConfig();
  checkDynamicSitemap();
  checkDynamicRobots();
  checkCanonicalUrls();

  console.log('🏁 SEO verification complete!');
  console.log('\n📝 Next Steps:');
  console.log('1. Deploy the changes to Vercel');
  console.log('2. Test robots.txt and sitemap.xml URLs');
  console.log('3. Submit sitemap to Google Search Console');
  console.log('4. Monitor indexing status in search engines');
  console.log('5. Verify canonical tags appear in browser dev tools');
}

// Run the verification
runAllChecks();
