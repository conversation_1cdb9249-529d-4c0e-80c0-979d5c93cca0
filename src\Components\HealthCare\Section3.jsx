import React from "react";

const Section3 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 p-4 md:p-8">
      <div className="container flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
            Secure and Compliant{" "}
            <span className="text-[#F245A1]">
              Software Development for Healthcare
            </span>
          </h2>
          <p className="text-base md:text-xl text-justify">
            Empower patient care access by implementing cutting-edge telehealth technologies that lower operating costs and get beyond mobility restrictions. We create virtual consultation rooms, integrate HL7, and adhere to DICOM rules to guarantee the secure and confidential transfer of medical data.
          </p>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-4 border border-purple-800 rounded-2xl shadow-md">
            <h3 className="font-semibold text-base md:text-xl ">
              With healthcare development services at Valueans, we offer:
            </h3>
            <div className="py-5 flex flex-col gap-3 md:gap-5 text-justify">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Total command over the data and system
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Functionality catered to your unique workflows
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">Reasonably priced up front</p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">Quick implementation</p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
                <p className="text-sm md:text-lg">
                  Extremely compatible with the same vendor's software
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section3;
