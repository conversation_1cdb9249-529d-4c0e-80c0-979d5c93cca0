import ServiceCard_2 from "@/Components/Card/ServiceCard_2";

import React from "react";

const Section4 = ({ cardData, heading, spanHeading, bluespanHeading, paragraph }) => {
  return (
    <div className="container mb-10 md:mb-24 my-12">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
        <span className="text-[#F245A1]">{spanHeading}</span> {heading} <span className="text-[#7716BC]">{bluespanHeading}</span>
        
      </h2>
      <p className="text-base md:text-lg text-center my-5">{paragraph}</p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 ">
        {cardData.map((card, index) => (
          <ServiceCard_2
            key={index}
            title={card.title}
            content={card.content}
          />
        ))}
      </div>
    </div>
  );
};

export default Section4;
