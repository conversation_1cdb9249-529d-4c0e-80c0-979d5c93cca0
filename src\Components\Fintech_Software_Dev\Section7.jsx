import React from "react";

const cards = [
  {
    title: "Increased Security and Thorough Regulations",
    description: "Security comes at a premium with all fintech solutions. Through our software, safe transactions are facilitated while protecting data, all in compliance with global financial laws."
  },
  {
    title: "Flexibility and Scalability",
    description: "Our solutions are built with the future in mind, which is why they are perpetually scalable and flexible in the face of changing market forces."
  },
  {
    title: "Focus on Users",
    description: "We emphasize customer satisfaction and interface enhancement regarding engagement."
  },
  {
    title: "Advanced Technologies",
    description: "AI, blockchain, and big data are some of the cutting-edge technologies we employ while developing sophisticated fintech solutions to ensure business success."
  },
  {
    title: "Low Code Automation",
    description: "Product development is accelerated with the use of existing libraries of code, making use of intuitive drag-and-drop interfaces. <a href='/Technologies/Low_Code_Development' class='text-[#7716BC] hover:underline'>Low code automation</a> reduces the time to market."
  }
];

const Card = ({ title, description }) => (
  <div className="w-full md:max-w-xl p-3 md:p-5 bg-white border rounded-md shadow-md overflow-hidden">
    <h2 className="text-[#7716BC] text-base md:text-lg font-semibold mb-1 md:mb-2">
      {title}
    </h2>
    <p
      className="text-sm md:text-base text-justify"
      dangerouslySetInnerHTML={{ __html: description }}
    />
  </div>
);

const Section7 = () => (
  <div className="container mb-10 md:mb-24">
    <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
      Why Valueans Deserves to Be Chosen Over Competitors in the{" "}
      <span className="text-[#F245A1]">
        Development of Fintech Software and Services
      </span>
    </h2>
    <p className="text-center text-lg md:text-xl mt-2">
      There are many benefits one can enjoy while working with Valueans in terms of developing fintech software:
    </p>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
      {cards.map((card, idx) => (
        <Card key={idx} {...card} />
      ))}
    </div>
  </div>
);

export default Section7;
