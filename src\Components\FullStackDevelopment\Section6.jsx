import React from "react";
import Card from "../Cloud Services/Card";

const Section6 = () => {
  const cardData = [
    {
      title: "Less Costly",
      description:
        "By managing both the front-end and back-end, full stack development reduces the need for multiple teams and helps companies save money on both development and operations. You may boost ROI and save expenses by using a unified approach.",
    },
    {
      title: "Comprehensive Solution",
      description:
        "By combining front-end and back-end development, our full stack services provide an all-in-one solution that produces a seamless, more efficient product. This guarantees improved cooperation throughout the development process and does away with the necessity to manage distinct services.",
    },
    {
      title: "Easy Code Management",
      description:
        "The front-end and back-end are coded in tandem and full stack development streamlines code management (utilizing <a href='/Technologies/Low_Code_Development' class='text-[#7716BC] hover:underline'> low code automation</a>) and improves the effectiveness of upgrades and maintenance. Additionally, this enhances the project's overall uniformity and maintainability.",
    },
    {
      title: "Increased Productivity",
      description:
        "Our developers operate on application’s both sides and our applied development methods guarantee faster delivery by removing delays frequently brought on by communication between several teams. This allows you to react to shifting company needs more quickly and results in a faster delivery time.",
    },
    {
      title: "Better Ability To Solve Problems",
      description:
        "As our full stack engineers have a comprehensive understanding of the application’s client and server sides, they are equipped to address complicated problems. As a result, problems are identified more quickly, and the system receives better fixes.",
    },
    {
      title: "Strong Data Security",
      description:
        "Strong security features integrated into both front-end and back-end systems are part of our full stack solutions. Our development efforts prioritize keeping confidential information safeguarded throughout the entire process. We use the most updated security procedures to protect your application from new dangers.",
    },
    {
      title: "Rapid Development",
      description:
        "By working across the entire stack, full stack developers expedite the process. It enables quicker product releases and iterations. You can be at the top of your game and quickly adjust to market demands, thanks to this agility.",
    },
    {
      title: "User-Friendly Applications",
      description:
        "At Valueans, we build applications that offer a smooth user experience on all platforms and devices in addition to being intuitive. We place a high value on <a href='/ui-ux' class='text-[#7716BC] hover:underline'> user-centric design</a> , emphasizing engagement, simplicity, and ease of navigation to make sure your audience can connect with your product with ease.",
    },
  ];

  return (
    <section className="bg-pink-100 py-5 md:py-10 my-5 md:my-24">
      <div className="container">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold mb-3">
          How <span className="text-[#F245A1]">Valueans </span>Full Stack
          Development <br /> Services Help You
        </h2>
        <div className="flex flex-col items-center">
          {/* First two rows (3 cards each) */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 ">
            {cardData.map((card, index) => (
              <Card
                key={index}
                title={card.title}
                description={card.description}
              />
            ))}
          </div>

      
        </div>
      </div>
    </section>
  );
};

export default Section6;
