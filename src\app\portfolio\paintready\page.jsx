import React from "react";
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: " Job Management & Team Collaboration Software | Valueans",
  description: "Cross-platform enterprise software that streamlines communication, job scheduling, and workflow management for painting enterprises using Django and React.",
  alternates: {
    canonical: "https://valueans.com/portfolio/paintready",
  },
  openGraph: {
    title: " Job Management & Team Collaboration Software | Valueans",
    description: "Cross-platform enterprise software that streamlines communication, job scheduling, and workflow management for painting enterprises using Django and React.",
    url: "https://valueans.com/portfolio/paintready",
    type: "website",
  },
};
const page = () => {
  const images = [
  '/Images/PR2.png',
  '/Images/PR3.png',
  '/Images/PR4.png',
];
  return (
    <div>
      <Section1 backgroundImage={"/Images/PaintReady12.png"} />
      <Section2
        image={"/Images/PR1.png"}
        heading={"Paint Ready"}
        paragraph1={
          "At Valueans, our team of dedicated developers has successfully delivered enterprise software development solutions to many clients. One of the most prominent projects is Paint Ready, which was designed to help a painting enterprise. Paint Ready is a hybrid program facilitating smooth team interaction and several other useful processes across desktop and mobile devices."
        }
        paragraph2={
          "Paint Ready helps in making several painting-related tasks smooth and easy. Our enterprise software development services helped Paint Ready to smartly allocate and monitor jobs, efficiently communicate between different teams, and effectively help with job scheduling and management."
        }
      />
      <Section4
        paragraph1={
          "We used a contemporary technology stack to provide a scalable and dependable solution. To offer a reliable and user-friendly platform, we used Django for developing the back end and React for developing the front end for Paint Ready."
        }
        paragraph2={
          "With our extensive technical knowledge and strong strategic perspectives, we created a bespoke software development platform that enhanced operational workflows leading to better lucrative outcomes."
        }
      />
      <Section5 images={images} />
    </div>
  );
};

export default page;
