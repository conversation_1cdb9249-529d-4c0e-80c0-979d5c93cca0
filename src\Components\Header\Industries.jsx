"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";

const Industries = ({ onNavigate }) => {
  // Enhanced navigation handler for dropdown items
  const handleEnhancedNavigation = (href, event) => {
    // Allow right-click (context menu) - don't prevent default
    if (event.button === 2) {
      return;
    }

    // Allow Ctrl+click (or Cmd+click on Mac) to open in new tab
    if (event.ctrlKey || event.metaKey) {
      window.open(href, '_blank');
      return;
    }

    // For normal left-click, prevent default and use custom navigation
    event.preventDefault();
    onNavigate(href);
  };
  return (
    <div className="md:container md:mx-auto md:p-10 md:border md:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Image Section */}
        <div className="col-span-1 md:col-span-2">
          <Image className="hidden md:block" src={"/Images/industries-nav.jpeg"} width={400} height={400} alt="Industries" />
        </div>
        
        {/* Links Section */}
        <div className="col-span-1 md:col-span-3 space-y-4 md:space-y-0 md:flex md:justify-between">
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/Industries/Healthcare"
              onClick={(e) => handleEnhancedNavigation("/Industries/Healthcare", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Healthcare", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Healthcare
            </Link>
            <Link
              href="/Industries/Gaming"
              onClick={(e) => handleEnhancedNavigation("/Industries/Gaming", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Gaming", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Gaming
            </Link>
            <Link
              href="/Industries/E-Commerce"
              onClick={(e) => handleEnhancedNavigation("/Industries/E-Commerce", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/E-Commerce", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              E-commerce
            </Link>
            <Link
              href="/Industries/Agriculture"
              onClick={(e) => handleEnhancedNavigation("/Industries/Agriculture", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Agriculture", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Agriculture
            </Link>
            <Link
              href="/Industries/Travel"
              onClick={(e) => handleEnhancedNavigation("/Industries/Travel", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Travel", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Travel
            </Link>
            <Link
              href="/Industries/Automotive"
              onClick={(e) => handleEnhancedNavigation("/Industries/Automotive", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Automotive", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Automotive
            </Link>
          </div>

          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/Industries/Finance"
              onClick={(e) => handleEnhancedNavigation("/Industries/Finance", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Finance", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Finance
            </Link>
            <Link
              href="/Industries/Education"
              onClick={(e) => handleEnhancedNavigation("/Industries/Education", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Education", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Education
            </Link>
            <Link
              href="/Industries/Social_Networking"
              onClick={(e) => handleEnhancedNavigation("/Industries/Social_Networking", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Social_Networking", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Social Networking
            </Link>
            <Link
              href="/Industries/Oil_And_Gas"
              onClick={(e) => handleEnhancedNavigation("/Industries/Oil_And_Gas", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Oil_And_Gas", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Oil and Gas
            </Link>
            <Link
              href="/Industries/Real_Estate"
              onClick={(e) => handleEnhancedNavigation("/Industries/Real_Estate", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Real_Estate", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Real Estate
            </Link>
            <Link
              href="/Industries/Insurance"
              onClick={(e) => handleEnhancedNavigation("/Industries/Insurance", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Insurance", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Insurance
            </Link>
          </div>

          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link
              href="/Industries/Telecom"
              onClick={(e) => handleEnhancedNavigation("/Industries/Telecom", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Telecom", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Telecom
            </Link>
            <Link
              href="/Industries/Construction"
              onClick={(e) => handleEnhancedNavigation("/Industries/Construction", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Construction", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Construction
            </Link>
            <Link
              href="/Industries/Manufacturing"
              onClick={(e) => handleEnhancedNavigation("/Industries/Manufacturing", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Manufacturing", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Manufacturing
            </Link>
            <Link
              href="/Industries/Logistics"
              onClick={(e) => handleEnhancedNavigation("/Industries/Logistics", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Logistics", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Logistics
            </Link>
            <Link
              href="/Industries/Banking"
              onClick={(e) => handleEnhancedNavigation("/Industries/Banking", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Industries/Banking", e)}
              className="text-left text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Banking
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Industries;
