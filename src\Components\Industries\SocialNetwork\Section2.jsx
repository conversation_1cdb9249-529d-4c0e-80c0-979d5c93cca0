import React from 'react'
import Image from 'next/image'

const Section2 = ({
  headingLeft,
  headingRight,
  spanHeading,
  image,
  paragraph,
  classname,
  heading2,
  paragraph2,
  paragraph3,
  paragraph4,
  bgColor
}) => {
  return (
      <div className={`${bgColor ? bgColor : "bg-[#F245A126]"} py-8 my-10`}>
      <div className="container mb-10 md:mb-24">
      <div className="my-8">
        <h2 className="text-xl md:text-3xl md:leading-[40px] md:w-[50%] mb-4 mx-auto text-center font-semibold">
          {headingLeft} <span className="text-[#F245A1]">{spanHeading}</span>{" "}
          {headingRight}
        </h2>
        <p className="text-base md:text-lg text-center">{paragraph}</p>
      </div>
      <div className={`flex flex-col  ${classname? classname : "md:flex-row"} justify-between gap-8`}>
        <div className="w-full md:w-[50%]">
          <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold mb-1 md:mb-3">
            {heading2}
          </h2>
          <p className="text-base md:text-lg text-justify mb-1 md:mb-3"> {paragraph2}</p>
          <p className="text-base md:text-lg text-justify mb-1 md:mb-3"> {paragraph3}</p>
          <p className="text-base md:text-lg text-justify mb-1 md:mb-3"> {paragraph4}</p>
          
        </div>
        <div className="md:w-[50%] min-h-[250px] md:min-h-0 w-full relative">
          <Image
            src={image}
            alt="AI"
            layout="fill" // Use layout="fill" to take up the entire space of the div
            objectFit="cover" // Ensures the image covers the entire space
          />
        </div>
      </div>
    </div>
      </div>
  )
}

export default Section2