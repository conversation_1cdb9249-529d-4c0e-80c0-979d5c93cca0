// SEO Configuration for Valueans
export const siteConfig = {
  name: 'Value<PERSON>',
  description: 'Valueans provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.',
  url: 'https://valueans.com',
  ogImage: '/Images/logo.png',
  links: {
    twitter: 'https://twitter.com/valueans',
    github: 'https://github.com/valueans',
    linkedin: 'https://linkedin.com/company/valueans',
  },
};

export const defaultMetadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  keywords: [
    'custom software development',
    'mobile app development',
    'web development',
    'AI/ML solutions',
    'digital transformation',
    'software consulting',
    'enterprise solutions',
    'SaaS development',
    'fintech solutions',
    'healthcare software',
  ],
  authors: [
    {
      name: 'Valueans',
      url: siteConfig.url,
    },
  ],
  creator: 'Valueans',
  metadataBase: new URL(siteConfig.url),
  alternates: {
    canonical: `${siteConfig.url}/`,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: siteConfig.name,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: '@valueans',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'valueans-verification-pending',
    yandex: 'valueans-yandex-verification',
    yahoo: 'valueans-yahoo-verification',
  },
};

// Structured Data for Organization
export const organizationStructuredData = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Valueans',
  url: siteConfig.url,
  logo: `${siteConfig.url}/Images/logo.png`,
  description: siteConfig.description,
  foundingDate: '2018',
  address: {
    '@type': 'PostalAddress',
    streetAddress: '16192 Coastal Highway',
    addressLocality: 'Lewes',
    addressRegion: 'Delaware',
    postalCode: '19958',
    addressCountry: 'US',
  },
  contactPoint: {
    '@type': 'ContactPoint',
    telephone: '+1-************',
    contactType: 'customer service',
    email: '<EMAIL>',
  },
  sameAs: [
    'https://linkedin.com/company/valueans',
    'https://twitter.com/valueans',
    'https://github.com/valueans',
  ],
  services: [
    'Custom Software Development',
    'Mobile App Development',
    'Web Development',
    'AI/ML Solutions',
    'Digital Transformation',
    'SaaS Development',
    'Fintech Solutions',
    'Healthcare Software',
  ],
};

// Structured Data for Website
export const websiteStructuredData = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: 'Valueans',
  url: siteConfig.url,
  description: siteConfig.description,
  publisher: {
    '@type': 'Organization',
    name: 'Valueans',
    logo: {
      '@type': 'ImageObject',
      url: `${siteConfig.url}/Images/logo.png`,
    },
  },
  potentialAction: {
    '@type': 'SearchAction',
    target: {
      '@type': 'EntryPoint',
      urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,
    },
    'query-input': 'required name=search_term_string',
  },
};

// Generate page-specific metadata
export function generatePageMetadata({
  title,
  description,
  path = '',
  image,
  noIndex = false,
}) {
  const pageUrl = `${siteConfig.url}${path}`;

  return {
    title,
    description,
    alternates: {
      canonical: pageUrl,
    },
    openGraph: {
      title,
      description,
      url: pageUrl,
      images: image ? [image] : [siteConfig.ogImage],
    },
    twitter: {
      title,
      description,
      images: image ? [image] : [siteConfig.ogImage],
    },
    robots: {
      index: !noIndex,
      follow: !noIndex,
    },
  };
}
