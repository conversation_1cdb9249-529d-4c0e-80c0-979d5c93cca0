import React from "react";
import Card from "./Card_2";

const Section7 = () => {
  const cardData = [
    {
      title: "Healthcare and Biotech",
      content: [
        "Patient Management Systems",
        "Telemedicine Platforms",
        "Healthcare Data Integration",
        "<a href='/Technologies/Predictive_Analysis' class='text-[#7716BC] hover:underline'> Predictive Analytics for Treatment</a>",
        "Medical Software Development",
      ],
    },
    {
      title: "Retail Industry",
      content: [
        "<a href='/Industries/E-Commerce' class='text-[#7716BC] hover:underline'>E-commerce Platforms</a>",
        "Customer Relationship Management (CRM)",
        "Inventory Management Systems",
        "Payment Gateway Integration",
        "Product Recommendation Systems",
      ],
    },
    {
      title: "Legal & Business Solutions",
      content: [
        "Custom Legal Platforms",
        "Document Automation",
        "Compliance Management Tools",
        "Contract Review Systems",
        "Legal Research Tools",
      ],
    },
    {
      title: "IT Services",
      content: [
        "End-to-End Web Development",
        "API Integration",
        "Cloud Solutions",
        "Scalable Architecture",
        "Custom Software Solutions",
      ],
    },
  ];

  const cardData_2 = [
    {
      title: "Energy and Resources",
      content: [
        "Smart Energy Management",
        "Predictive Maintenance",
        "<a href='/Data_and_Analytics' class='text-[#7716BC] hover:underline'> Data Analytics</a> for Resource Management",
        "IoT Integrations for Monitoring",
        "Workflow Automation",
      ],
    },
    {
      title: "Manufacturing Industry",
      content: [
        "Process Automation Solutions",
        "Custom ERP Systems",
        "Supply Chain Optimization",
        "Predictive Maintenance Platforms",
        "Robotics Integration for Manufacturing",
      ],
    },
    {
      title: "Real Estate Solutions",
      content: [
        "<a href='/Industries/Real_Estate' class='text-[#7716BC] hover:underline'>Property Management Software</a>",
        "Real Estate Market Analysis Tools",
        "Smart Building Systems",
        "Predictive Maintenance Systems",
        "Energy Efficiency Solutions",
      ],
    },
  ];

  // Combine both arrays into one
  const allCards = [...cardData, ...cardData_2];

  return (
    <div className="container my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Valueans Successfully{" "}
        <span className="text-[#F245A1]">Delivers Full Stack Development</span>{" "}
        Across Various Industries
      </h2>

      {/* Single grid: 1 column on mobile, 4 columns on md+ */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
        {allCards.map((card, index) => (
          <Card
            key={index}
            title={card.title}
            content={card.content}
            className="border-purple-200"
          />
        ))}
      </div>
    </div>
  );
};

export default Section7;
