import Image from "next/image";
import Button from "../Buttons/Button";
import PinkBgcard from "./PinkBgcard";

const Section7 = ({ cardData, heading, spanHeading, paragrapgh }) => {
  return (
    <div className="container mb-10 md:my-24 ">
      <h2 className="text-xl md:text-3xl text-center font-semibold mb-1">
        {heading} <span className="text-[#7716BC]">{spanHeading}</span>
      </h2>
      <p className="md:w-[85%] md:mx-auto mb-4 text-base md:text-xl text-center">
        {paragrapgh}
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-5">
        {cardData.map((card, index) => (
          <PinkBgcard
            key={index}
            title={card.title}
            description={card.description}
          />
        ))}
      </div>
    </div>
  );
};

export default Section7;
