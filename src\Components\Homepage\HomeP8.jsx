import React from "react";
import Button from "../Buttons/Button";
import Image from "next/image";
import Heading from "../Heading/Heading";

const HomeP8 = ({ heading, paragrapgh, paragraph2, buttonBg, buttonText, to }) => {
  return (
    <>
      <section className=" bg-[#7716BC] py-8">
        <div className="flex flex-col-reverse md:flex-row justify-center md:justify-between items-center container">
          <div className="md:w-[60%]">
            <h2 className="capitalize text-white text-2xl md:text-3xl leading-9 text-center md:text-left  font-extrabold">
              {heading}
            </h2>

            {paragrapgh && (
              <p
                className="w-full md:w-2/3 text-base md:text-lg text-justify text-white font-normal m-1 "
                dangerouslySetInnerHTML={{ __html: paragrapgh }}
              />
            )}
            <p className="w-full md:w-2/3 text-base md:text-lg text-justify text-white font-normal m-1 mb-10">
              {paragraph2}
            </p>
            <div className="w-fit mx-auto md:mx-0">
              <Button to={to} bgColor="bg-[#F245A1]">{buttonText}</Button>
            </div>
          </div>
          <div className=" relative">
            <Image
              src="/Images/estimate.svg"
              alt="estimate"
              className="flex-grow-0"
              width={400}
              height={400}
              objectFit="cover"
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default HomeP8;
