const Card = ({ title, description, bgColor = "bg-white" }) => {
  return (
    <div
      className={`block w-full md:w-[307px] h-auto md:h-[282px] p-4 ${bgColor} border border-gray-200 rounded-lg shadow-md text-justify`}
    >
      <h5 className="text-[#7716BC] text-lg  font-bold tracking-tight mb-1  md:mb-3">
        {title}
      </h5>
      <p className="font-normal text-sm  md:text-base">{description}</p>
    </div>
  );
};

const Section8 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-2xl md:text-3xl md:leading-[40px] text-center font-semibold mb-[24px] md:mb-[42px]">
        The Benefits of Valueans{" "}
        <span className="text-[#F245A1]">Manual Software Testing Services</span>
      </h2>
      <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-[18px]">
        <Card
          title={"Better User Experiences"}
          description={
            "Our world-class manual testing services employ patented testing scenarios and real-user behavior simulation to provide users with the best software possible. Feedback from possible users can also be found in QA reports."
          }
        />
  
        <Card
          title={"Process of Real-Time Testing"}
          description={
            "We test software from the very beginning of its development and make small modifications without actually writing the code and running it in order to identify issues early."
          }
          bgColor="bg-blue-100"
        />
        <Card
          title={"Cost Effectiveness"}
          description={
            "Because we offer a range of genuine devices, emulators, browser stacks, clouds, and other tools that make manual software testing easier and lower costs, it's ideal for projects with limited resources."
          }
        />
        <Card
          title={"Flexible Cooperation Models"}
          description={
            "You may select the model that best fits your aims, timeframes, and financial constraints, whether it's outsourcing, a dedicated staff, an augmentation team, managed services, or QA consultancy."
          }
          bgColor="bg-blue-100"
        />
      </div>
    </div>
  );
};

export default Section8;
