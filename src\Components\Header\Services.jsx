"use client";
import React from "react";
import Link from "next/link";

const Services = ({ onNavigate }) => {
  // Enhanced navigation handler for dropdown items
  const handleEnhancedNavigation = (href, event) => {
    // Allow right-click (context menu) - don't prevent default
    if (event.button === 2) {
      return;
    }

    // Allow Ctrl+click (or Cmd+click on Mac) to open in new tab
    if (event.ctrlKey || event.metaKey) {
      window.open(href, '_blank');
      return;
    }

    // For normal left-click, prevent default and use custom navigation
    event.preventDefault();
    onNavigate(href);
  };
  return (
    <div className="container mx-auto p-10 border rounded-lg">
      <div className="grid grid-cols-6 gap-4 ">
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold"> Custom Software Solutions</h4>
            <Link
              href="/custom-software-development"
              onClick={(e) => handleEnhancedNavigation("/custom-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/custom-software-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Software Development
            </Link>
            <Link
              href="/custom-website-development"
              onClick={(e) => handleEnhancedNavigation("/custom-website-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/custom-website-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Custom Web Development
            </Link>
            <Link
              href="/saas-app-development"
              onClick={(e) => handleEnhancedNavigation("/saas-app-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/saas-app-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Saas Development
            </Link>
            <Link
              href="/Full_Stack_Development_Services"
              onClick={(e) => handleEnhancedNavigation("/Full_Stack_Development_Services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Full_Stack_Development_Services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Full Stack Development
            </Link>
            <Link
              href="/mobile-app-development"
              onClick={(e) => handleEnhancedNavigation("/mobile-app-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/mobile-app-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Mobile App Development
            </Link>
            <Link
              href="/financial-app-development"
              onClick={(e) => handleEnhancedNavigation("/financial-app-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/financial-app-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Fintech
            </Link>
            <Link
              href="/health-care-development"
              onClick={(e) => handleEnhancedNavigation("/health-care-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/health-care-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Healthcare Development
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">IT Consulting</h4>
            <Link
              href="/AI"
              onClick={(e) => handleEnhancedNavigation("/AI", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/AI", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              AI
            </Link>
            <Link
              href="/App_Integration"
              onClick={(e) => handleEnhancedNavigation("/App_Integration", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/App_Integration", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Application Integration
            </Link>
            <Link
              href="/Cloud_Services"
              onClick={(e) => handleEnhancedNavigation("/Cloud_Services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Cloud_Services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Cloud Services
            </Link>
            <Link
              href="/Business_Intelligence"
              onClick={(e) => handleEnhancedNavigation("/Business_Intelligence", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Business_Intelligence", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Business Intelligence
            </Link>
            <Link
              href="/product-management"
              onClick={(e) => handleEnhancedNavigation("/product-management", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/product-management", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Product Management
            </Link>
            <Link
              href="/ML"
              onClick={(e) => handleEnhancedNavigation("/ML", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/ML", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              ML
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Testing & QA</h4>
            <Link
              href="/Quality_Assurance"
              onClick={(e) => handleEnhancedNavigation("/Quality_Assurance", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Quality_Assurance", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Quality Assurance & Testing
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Data Solutions</h4>
            <Link
              href="/Data_and_Analytics"
              onClick={(e) => handleEnhancedNavigation("/Data_and_Analytics", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Data_and_Analytics", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Data Analytics
            </Link>
            <Link
              href="/DataEngineering"
              onClick={(e) => handleEnhancedNavigation("/DataEngineering", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/DataEngineering", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Data Engineering
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Design Services</h4>
            <Link
              href="/ui-ux"
              onClick={(e) => handleEnhancedNavigation("/ui-ux", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/ui-ux", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              UI UX designing
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Application Services</h4>
            <Link
              href="/Maintenance_and_Support"
              onClick={(e) => handleEnhancedNavigation("/Maintenance_and_Support", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Maintenance_and_Support", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Maintainence and Support
            </Link>
            <Link
              href="/Dedicated_Deployment_teams"
              onClick={(e) => handleEnhancedNavigation("/Dedicated_Deployment_teams", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Dedicated_Deployment_teams", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Dedicated Deployment Teams
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Services;
