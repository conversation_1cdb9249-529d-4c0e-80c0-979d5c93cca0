import React from "react";

const Section6 = ({ heading, paragraph, cardData }) => {
  return (
    <div className="container mb-10 md:my-24 mt-20">
      <div className="my-5 text-center">
        <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
          {heading}
        </h2>
        <p className="text-base md:text-lg">{paragraph}</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 w-full gap-8">
        {cardData.map((card, index) => (
          <div
            key={index}
            className="border border-[#F245A1] rounded-lg bg-white p-4 min-w-lg mx-auto"
          >
            <h3 className="text-base my-2 font-semibold">{card.title}</h3>
            <p className="text-sm mb-2 text-justify">{card.description}</p>

            {/* List mapping here */}
            <ul className="list-disc list-inside text-sm text-gray-700 mt-3">
              {card.list.map((item, idx) => (
                <li key={idx}>{item}</li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Section6;
