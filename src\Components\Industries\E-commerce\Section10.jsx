import PinkDotCard2 from "../HealthCare/PinkDotCard2";
import React from "react";
import Image from "next/image";

const Section10 = ({ heading1, paragraph1, PinkDotCardData, PinkDotCardData2, PinkDotCardData3, heading2, heading3,paragraph2, paragraph3 }) => {
  return (
    <div className="container mb-10 md:my-24 mt-20">
      <div className="bg-[#F245A126] p-8 flex flex-col md:flex-row md:justify-between gap-8">
        <div className="md:w-[40%] bg-[#794CEC26] p-4 rounded-md">
          <div className="p-2">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading1}
            </h2>
            <p className="text-base md:text-lg text-justify ">{paragraph1}</p>
          </div>
          <PinkDotCard2 cardContent={PinkDotCardData} />
        </div>
        <div className="md:w-[40%] bg-[#F245A126] p-4 rounded-md">
          <div className="p-2">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading2}
            </h2>
            <p className="text-base md:text-lg text-justify ">{paragraph2}</p>
          </div>
          <PinkDotCard2 cardContent={PinkDotCardData2} />
        </div>
      </div>
      <div className="bg-[#794CEC26] p-8 flex flex-col md:flex-row md:justify-center mt-10 gap-8">
        <div className="md:w-[40%] bg-[#794CEC26] p-4 rounded-md">
          <div className="p-2">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading3}
            </h2>
            <p className="text-base md:text-lg text-justify ">{paragraph3}</p>
          </div>
          <PinkDotCard2 cardContent={PinkDotCardData3} />
        </div>
      </div>
    </div>
  );
};

export default Section10;
