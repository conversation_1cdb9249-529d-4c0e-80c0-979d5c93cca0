import React from "react";

const HorizontalColoredCard = ({
  bgColor,
  heading,
  paragraph,
  listContent,
}) => {
  return (
    <div className={`${bgColor ? bgColor : ""} p-4 space-y-4 rounded-md`}>
      <h3 className="text-lg md:text-xl font-semibold">{heading}</h3>

      {paragraph && (
        <p
          className="text-sm md:text-base text-justify"
          dangerouslySetInnerHTML={{ __html: paragraph }}
        />
      )}
      <ul className="list-disc ml-4">
        {listContent?.map((item, index) => (
          <li
            key={index}
            className="text-sm md:text-base"
            dangerouslySetInnerHTML={{ __html: item || "" }}
          />
        ))}
      </ul>
    </div>
  );
};

export default HorizontalColoredCard;
