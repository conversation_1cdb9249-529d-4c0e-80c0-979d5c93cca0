
import SecondBlogDisplaySection from "@/Components/Blog/SecondBlogDisplaySection";
import TopBlogDisplay from "@/Components/Blog/TopBlogDisplay";
import Blog from "@/Components/pages/Blog";

import React from "react";
export const metadata = {
  title: "Stay Informed of engaging Articles and Trends with Our Blog",
  description: "Dive into our blog to get insights into trends and solutions for custom software development. Stay tuned with our extensive research and creative solutions.",
  alternates: {
    canonical: "https://valueans.com/blog",
  },
  openGraph: {
    title: "Stay Informed of engaging Articles and Trends with Our Blog",
    description: "Dive into our blog to get insights into trends and solutions for custom software development. Stay tuned with our extensive research and creative solutions.",
    url: "https://valueans.com/blog",
    type: "website",
  },
};
const Blogpage = () => {
  return (
    <div>
      <TopBlogDisplay />
      <SecondBlogDisplaySection />
      {/* <BlogDisplaySection blogs={blogPosts} /> */}
      <Blog title="Technology related news/Blogs" />
    </div>
  );
};

export default Blogpage;
