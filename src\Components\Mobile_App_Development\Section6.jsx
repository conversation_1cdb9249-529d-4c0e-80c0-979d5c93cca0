import React from "react";

const cards = [
  {
    title: "Healthcare",
    description:
      "Mobile health apps may improve medical services, boost patient involvement, and streamline operational procedures.",
  },
  {
    title: "Insurance",
    description:
      "Use mobile technology created with your industry in mind to provide improved insurance solutions and services.",
  },
  {
    title: "Finance",
    description:
      "Mobile fintech software may provide you with a competitive advantage in any field, including trading, loan management, and investment banking.",
  },
  {
    title: "Manufacturing",
    description:
      "We provide useful mobile solutions to manage supply chains, production lines, and customer support for the industrial sector.",
  },
  {
    title: "Education",
    description:
      "Use custom app development to support online learning procedures and guarantee a mobile experience for instructors and students.",
  },
  {
    title: "Transportation",
    description:
      "Mobile applications help businesses in the transportation sector run their fleets and logistics more efficiently.",
  },
  {
    title: "Real Estate",
    description:
      "We use mobile technologies to help all real estate market participants, from agents and sellers to homebuyers, succeed.",
  },
  {
    title: "Retail & eCommerce",
    description:
      "To attract online customers and guarantee business growth, offer in-person experiences and use mobile apps to streamline retail procedures.",
  },
];

const Card = ({ title, description }) => (
  <div className="w-full p-4 rounded-md shadow-sm border border-purple-500 bg-white">
    <h3 className="text-base md:text-lg font-semibold mb-2">{title}</h3>
    <p className="text-sm md:text-base text-justify">{description}</p>
  </div>
);

const Section6 = () => (
  <div className="container mb-10 md:mb-24">
    <h2 className="text-xl md:text-3xl md:leading-9 text-center font-semibold">
      <span className="text-[#F245A1]">Valueans</span> Covers a Wide Range of Industries
    </h2>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6 md:mt-[42px]">
      {cards.map((card, idx) => (
        <Card key={idx} {...card} />
      ))}
    </div>
  </div>
);

export default Section6;
