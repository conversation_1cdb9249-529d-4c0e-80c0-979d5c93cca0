import { <PERSON>pins, Trirong, Aclonica, Titillium_Web } from "next/font/google";
import Script from "next/script";
import "./globals.css";
import Navbar from "@/Components/Header/Navbar";
import Footer from "@/Components/Footer/Footer";
import { Toaster } from "react-hot-toast";

import { defaultMetadata, organizationStructuredData, websiteStructuredData } from "@/lib/seo-config";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
});

const trirong = Trirong({
  subsets: ["latin"],
  weight: ["400", "700"],
  display: "swap",
  variable: "--font-trirong",
});

const aclonica = Aclonica({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-aclonica",
});

const titillium = Titillium_Web({
  subsets: ["latin"],
  weight: ["300", "400", "600"],
  variable: "--font-titillium",
});

export const metadata = defaultMetadata;

export default function RootLayout({ children }) {
  return (
    <html
      lang="en"
      className={`${poppins.variable} ${trirong.variable} ${aclonica.variable} ${titillium.variable}`}
    >
      <head>
        <link rel="icon" href="/Images/favicon.ico" type="image/x-icon" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationStructuredData)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteStructuredData)
          }}
        />
        <script src="https://analytics.ahrefs.com/analytics.js" data-key="2LzS9D1C5mRlDzjSZCZMKQ" async></script>
        <script src="https://js.stripe.com/v3/pricing-table.js" async></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "s1x1q3sw18");
            `
          }}
        />
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-DM1CD09K74"></script>
        {/* Tawk.to Chat Widget */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
              (function(){
                var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
                s1.async=true;
                s1.src='https://embed.tawk.to/65d3e19f8d261e1b5f628230/1hn1qim2h';
                s1.charset='UTF-8';
                s1.setAttribute('crossorigin','*');
                s0.parentNode.insertBefore(s1,s0);
              })();
            `
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','GTM-WV9GJZSS');
            `,
          }}
        />
      </head>
      <body className={`${poppins.className} text-gray-900 antialiased bg-[#f7fbfb]`}>
         <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-WV9GJZSS"
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
          ></iframe>
        </noscript>
        <Navbar />
        <Toaster position="top-center" />
        {children}
        <Footer />
        <script type="text/javascript" id="hs-script-loader" async defer src="//js-na2.hs-scripts.com/243114810.js"></script>
      </body>
    </html>
  );
}
