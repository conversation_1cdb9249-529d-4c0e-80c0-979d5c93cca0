import React from "react";
import PinkDotCard from "@/Components/PWA_development/PinkDotCard";
import BoldSpanPinkDotCard from "./BoldSpanPinkDotCard";

const Section7 = ({ bgColor, heading, paragraph, cardData }) => {
  return (
    <div className={`${bgColor ? bgColor : ""} py-5 my-16`}>
      <div className="container">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
          <div className="md:w-[40%]">
            <h2 className="text-xl md:text-3xl md:leading-[40px]  font-semibold">
              {heading}
            </h2>
            <p className="text-base md:text-xl ">{paragraph}</p>
          </div>
          <div className="md:w-[60%]">
            <BoldSpanPinkDotCard cardContent={cardData}/>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section7;
