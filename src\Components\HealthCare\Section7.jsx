import React from "react";
import ServiceCount from "../Services/ServiceCount_2";

const steps = [
  {
    number: 1,
    title: "Consulting",
    description:
      "By assisting you in selecting the solution that best satisfies your business's essential needs, offering guidance on platforms or technologies that complement your company's ecosystem, and creating an effective implementation strategy, our advisors help you create the finest possible digital environments.",
  },
  {
    number: 2,
    title: "Implementation",
    description:
      "The staff at Implementation Valueans is prepared to provide any kind of healthcare solution in line with your company's goals. We use best practices for application deployment, considering industry and security standards while following the approach that works best for your situation.",
  },
  {
    number: 3,
    title: "Integration",
    description:
      "By safely connecting all of your clinical and administrative software solutions and with external apps, we guarantee the interoperability of your healthcare IT environment.",
  },
  {
    number: 4,
    title: "Support & Maintenance",
    description:
      "Our professionals assist healthcare organizations in maintaining the cost-effectiveness, flexibility, security, and full functionality of their software. We offer a variety of support services, such as audits, round-the-clock performance monitoring, and prompt updates and modifications.",
  },
  {
    number: 5,
    title: "Legacy Software Modernization",
    description:
      "We assist healthcare organizations in modernizing, expanding, or rewriting their outdated software. Your present software may be improved by our developers to become more dependable, secure, and standard-compliant.",
  },
];

const Card = ({ title, description }) => (
  <div className="w-full md:max-w-xl h-auto md:h-[150px] py-2 px-3 bg-white border border-purple-500 rounded-md shadow">
    <h3 className="text-base font-semibold mb-1">{title}</h3>
    <p className="text-sm font-light text-justify">{description}</p>
  </div>
);

const Section7 = () => (
  <section className="bg-pink-100 mb-10 md:mb-24 py-4 md:py-10">
    <div className="container">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold mb-3">
        Our Process
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
        {steps.map((step) => (
          <div
            key={step.number}
            className={`flex items-center gap-2 ${
              step.number === 5 ? "md:col-span-2 md:justify-center" : ""
            }`}
          >
            <ServiceCount>{step.number}</ServiceCount>
            <Card title={step.title} description={step.description} />
          </div>
        ))}
      </div>
    </div>
  </section>
);

export default Section7;
