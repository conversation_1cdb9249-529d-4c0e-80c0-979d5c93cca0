import React from "react";
import Card from "./Card_3";

const Section7 = () => {
  const cardData = [
    {
      title: "Control Expenses",
      description:
        "Adapt to the increasing complexity of the IT environment while keeping business operations and critical application lifecycle management with zero downtime and incredibly low costs. Analyze and evaluate systems and implement suggestions for re-architecting to lower total cost of ownership through user-friendly application project management.",
    },
    {
      title: "Potential for Innovation",
      description:
        "Utilize technological advancements for application development services to increase return on investment while ensuring 24/7 support and effective company procedures. Discover the unrealized potential of your current application by doing thorough consultations to determine the best app management techniques to fully realize your application's potential in the marketplace.",
    },
    {
      title: "Remain Relevant",
      description:
        "In the constantly changing application lifecycle management ecosystem, take advantage of cutting-edge technology, industry best practices, and the newest tools available to deliver best-in-class customer experiences and a final product that satisfies all specifications, requirements, and business objectives.",
    },
    {
      title: "Prepare for the Cloud",
      description:
        "Participate in the cloud migration as a prerequisite for application performance management and cloud app integration. Stay ahead of the curve with scalability and flexibility that apply to all application tech components and service offerings through comprehensive application project management. To power your computer, you may select between specialized 'boutique' clouds or well-known cloud monoliths like Azure, AWS, and their kin.",
    },
    {
      title: "Adopt Security and Agility",
      description:
        "Reimagine app development and delivery by combining the advantages of agility with DevSecOps for quicker operations, QA, and IT security. Agile and successful application management requires overcoming operational and financial obstacles while constantly adjusting features and service offerings to meet shifting consumer and market demands.",
    },
    {
      title: "Dynamic Scaling",
      description:
        "For efficient application performance management and the adaptability of scaling projects up or down, embrace application project management thought leadership in relation to design thinking, microservices, and service architecture principles. Implement business insights acquired via consultative approaches to enhance revenue and improve processes.",
    },
    {
      title: "Better Application Optimization and Stability",
      description:
        "By integrating numerous systems into a singular solution, you improve the efficiency of the QA processes, streamline teamwork, and reduce redundancy. Furthermore, these aspects assure peak application stability, responsiveness, and optimum performance value management throughout the application.",
    },
    {
      title: "Transparent Integration",
      description:
        "For maximum functionality and improved business results, develop, innovate, and integrate with third-party systems. Increase productivity by making sure the proper resources, whether they are development teams, technological assets, or third-party APIs, are mobilized for specific activities. This will enable your business application to have a unique app administration that supports readily incorporable application assets.",
    },
  ];
  return (
    <div className="container my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
        Why Choose Valueans for{" "}
        <span className="text-[#F245A1]">Web Application Integration</span>{" "}
      </h2>
      <div className="container mx-auto p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {cardData.map((card, index) => (
            <Card
              key={index}
              title={card.title}
              description={card.description}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section7;
