import React from "react";

const cards = [
  {
    title: "Consultation",
    description:
      "At Valueans, our team of experts is always there to listen to your queries and offer the most suitable solution for your business. We ensure that you’re fully satisfied right from the first call to the deployment of an ML solution.",
  },
  {
    title: "Modeling",
    description:
      "Our team is trained to prepare datasets for effective ML modeling and algorithm training by taking data collection, cleansing, and structuring off of your hands so your business gets what it deserves without any hassle.",
  },
  {
    title: "Implementation",
    description:
      "Whether it’s a new model or existing enterprise software, our skilled engineers are always ready to provide you with the latest and high-performing custom machine-learning solutions or integrate them to the next level.",
  },
  {
    title: "Support",
    description:
      "At Valueans, we support businesses in maintaining and improving the effectiveness of machine learning models, we keep an eye on key performance indicators, carry out testing, and evaluate the outcomes to guarantee ongoing progress.",
  },
];

const Card = ({ title, description }) => (
  <div className="w-full md:max-w-md h-auto md:h-[220px] p-3 md:p-5 bg-white border rounded-md shadow-md overflow-hidden">
    <h2 className="text-[#7716BC] text-base md:text-lg font-semibold mb-1 md:mb-2">
      {title}
    </h2>
    <p className="text-sm md:text-base text-justify">{description}</p>
  </div>
);

const Section7 = () => (
  <div className="container mb-10 md:mb-24">
    <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
      Our Approach to{" "}
      <span className="text-[#7716BC]">Custom Machine Learning Solutions</span>
    </h2>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6 md:mt-8 justify-items-center">
      {cards.map((card, idx) => (
        <Card key={idx} {...card} />
      ))}
    </div>
  </div>
);

export default Section7;
