
import Box from "@mui/material/Box"
import EstimateHeader from "@/Components/EstimateHeader"

export const metadata = {
  title: "Application and website maintenance services for seamless operations",
  description: "Valueans provides ongoing app maintenance and website maintenance packages for uninterrupted performance and efficiency",
  keywords: [
    "Software Maintenance",
    "System Updates",
    "Technical Support",
    "Valueans Services",
    "Performance Optimization",
    "Reliable Software Solutions",
    "Maintenance and Support",
    "Application Maintenance",
    "IT Support Services",
    "Valueans Maintenance"
  ],
  alternates: {
    canonical: `https://valueans.com/maintenance`,
  },
  og: {
    title: "Expert Maintenance Services at Valueans",
    description: "Valueans provides top-notch maintenance services to ensure your software systems are efficient, up-to-date, and secure.",
    type: "website",
    url: "https://www.valueans.com/maintenance",
    image: "https://www.valueans.com/logo/logo1.svg"
  }
};

const Page = () => {
  return (
    <>
      <EstimateHeader title="Expert Maintenance Services for Seamless Operations" description="Entrust your digital assets to Valueans' maintenance services for uninterrupted performance and efficiency. Our comprehensive maintenance solutions ensure your systems are always up-to-date, secure, and running smoothly, minimizing downtime and maximizing productivity." propVal={false}/>
      <Box mt={6} padding="40px">
        <stripe-pricing-table pricing-table-id={process.env.STRIPE_MAINTENANCE_TABLE_ID}
          publishable-key={process.env.STRIPE_PUBLISH_KEY}>
          </stripe-pricing-table>
      </Box>
      </>
  )
}

export default Page