import React from "react";
import Card from "./Card_2";

const cards = [
  {
    title: "Industry-Specific Solutions",
    description:
      "We are aware of the data difficulties in your industry. Our data engineering specialists assist you in developing data solutions that satisfy industry standards and offer value. ",
  },
  {
    title: "Quick Insights",
    description:
      "Smarter choices may be made throughout your business with the support of our digital and technical advice solutions, which help you swiftly create insights. ",
  },
  {
    title: "Smart Automation",
    description:
      "Automate data flows to reduce mistakes and save money. By doing this, your staff can focus on important tasks and become more productive. ",
  },
  {
    title: "Future-Ready Architecture",
    description:
      "Create a robust data environment that can grow with your company. Our robust, high-end solutions address present issues and save costly changes later.",
  },
  {
    title: "Strong Governance",
    description:
      "Make sure your data procedures are sound and in line with the law. By doing this, you may reduce risks, adhere to rules, and gain confidence in your data-driven choices.",
  },
];

const Section6 = () => (
  <div className="container mb-10 md:mb-24">
    <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
      Why Choose Valueans’{" "}
      <span className="text-[#F245A1]">Data Engineering Solutions</span>
    </h2>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6 md:mt-8 justify-items-center">
      {cards.map((card, idx) => (
        <Card key={idx} title={card.title} description={card.description} />
      ))}
    </div>
  </div>
);

export default Section6;
