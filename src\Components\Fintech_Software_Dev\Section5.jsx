import Image from "next/image";

const InfoCard = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-xl border border-pink-500  p-2 md:p-4 rounded-2xl shadow">
      <div className="w-full  flex items-start gap-1 mb-4">
        <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
          <Image
            src="/Images/service_frame.png"
            alt="Tick"
            width={32}
            height={32}
            className="w-full h-full flex-shrink-0"
          />
        </div>
        <div className="w-[100%]">
          <h3 className="text-base md:text-lg font-semibold">{title}</h3>
          <p className="text-sm md:text-base text-justify">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
};

const Section5 = () => {
  return (
    <section className="bg-blue-100 mb-10 md:mb-24 py-4 md:py-10">
      <div className="container">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          Fintech Software Development Issues
        </h2>
        <p className="w-full  md:mx-auto text-base md:text-xl text-center">
          Although there is a lot of growth in fintech today, many businesses have problems when creating financial products. Some of these problems include the following:
        </p>
        <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-6 md:my-[42px] ">
          <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6">
            <InfoCard
              title={"Compliance"}
              description={
                "Banking and finance are sectors that have a lot of constraints in terms of compliance and regulations, and so do the fintech firms. Fintech, like any other business operating globally, must follow the local legal framework governing their target markets. GDPR, PCI DSS, and AML are examples of law that, when violated, can lead to severe penalties – financial fines, loss of business trust, or severe criminal consequences. As a result of insufficient research and development in some companies, the violation of these laws has cost firms dearly. "
              }
            />
            <InfoCard
              title={"Cybersecurity Risks"}
              description={
                "With more financial services offered online, there are more opportunities for cybercriminals. Fintech solutions should include enhanced identity and access management (IAM) capabilities like encryption, multi-factor authentication, and biometrics to safeguard against unauthorized access. Other security measures like fraud detection systems, data loss prevention, and anti-identity theft safeguards are necessary to protect against breaches and impersonation. "
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard
              title={"Scalability and Performance"}
              description={
                "Fintech Software should be capable of processing high data and transaction volumes concurrently. In such cases, the software must be scalable and achieve the best results even at the hardest thresholds. For software developers, ensuring that the software is scaled and works well during peak times is particularly difficult. "
              }
            />
            <InfoCard
              title={"Adoption and User Experience  "}
              description={
                "The introduction of a new set of fintech products should not require users to change their behaviors. New realities are achieved through intuitive user interfaces, efficient performance, and fast onboarding techniques. Usage of the platform might, however, be turned off by poorly developed interfaces and lengthy waiting times. "
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard
              title={" Incorporating Older Systems"}
              description={
                "Several banks and financial institutions continue to use legacy systems that may not accommodate these cutting edge fintech solutions. Integration with existing technology using evolution infrastructure without disrupting data continuity can be an arduous undertaking. "
              }
            />
            <InfoCard
              title={
                "Privacy as it Relates to Data and Fintech Cloud Management "
              }
              description={
                "So much sensitive data is involved, and therefore strong protective policies and secure data storing mechanisms must be in place. Fintech companies must invest in data encryption alongside tight APIs and access control."
              }
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section5;
