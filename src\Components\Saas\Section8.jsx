import React from "react";

const Section8 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Benefits of{" "}
        <span className="text-[#F245A1]">
          SaaS Software Development Services
        </span>
      </h2>
      <p className="text-base md:text-xl text-center w-full md:w-[80%] mx-auto my-1 md:my-3">
        As a SaaS software development company, we believe that for our SaaS product to be successful, it should present proper solutions to genuine problems, have a great user interface, expand together with its audience, and protect sensitive information. That is how we create products that make a difference and build trust.
      </p>
      <div className="w-full md:max-w-lg mx-auto my-5 md:my-10">
        <section className="w-full  mx-auto p-4 border border-purple-500 rounded-2xl shadow-sm">
          <div className="py-5 flex flex-col gap-3 text-justify md:gap-5">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Increased productivity with streamlined automation
              </p>
            </div>
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Cost efficiency by removing the need for physical
                infrastructure. 
              </p>
            </div>
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Better collaboration with real-time data access.
              </p>
            </div>
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Quick deployment and seamless scalability.
              </p>
            </div>
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Availability of data analytics and insights for informed 
                decision-making.
              </p>
            </div>
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-4 md:w-5 h-4 md:h-5 mt-1 bg-[#F245A1]"></div>
              <p className="text-sm md:text-lg">
                Minimized risk with a secure backup and recovery plan.
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Section8;
