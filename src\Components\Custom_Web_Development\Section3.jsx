import React from "react";

const Section3 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 p-4 md:p-8">
      <div className="container flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
            Valueans{" "}
            <span className="text-[#7716BC]">Web Application Development</span>{" "}
            Services Drive Success
          </h2>
          <p className="text-base md:text-xl text-justify">
            To create a high-performing website, collaborate with a reputable custom website development company. Make a digital experience that produces outcomes, from captivating business websites to intricate animations. Make use of our skilled designers' knowledge and inventiveness to help your brand reach quantifiable outcomes.
          </p>
        </div>
        <div className="flex-1">
          <section className="w-full  mx-auto p-4 border border-purple-800 rounded-2xl shadow-md">
            <h3 className="font-semibold text-base md:text-xl ">
              With custom web development services at Valueans, we offer:
            </h3>
            <div className="py-5 flex flex-col gap-3 md:gap-5">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0  w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Agile approach to web design and development
                </p>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex-shrink-0  w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Websites and solutions that have won awards
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0  w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Competitive prices for skilled web designers and developers
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0  w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Cross-functional group of more than 300 professionals
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0  w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">Front-end specialists</p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0  w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  A track record of successfully completing web design and
                  development projects
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0  w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Thorough testing for quality assurance (QA)
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0  w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Reduced expenses due to quicker development timeframes
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-4  h-4 mt-2 bg-[#F245A1]"></div>
                <p className="text-sm md:text-base">
                  Unmatched knowledge and experience in open technologies
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default Section3;
