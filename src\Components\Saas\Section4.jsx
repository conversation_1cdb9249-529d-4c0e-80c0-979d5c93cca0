import React from "react";

const Card = ({ title, description, bgColor = "bg-white" }) => {
  return (
    <div
      className={`w-full md:w-auto p-4 ${bgColor} border border-gray-200 rounded-lg shadow-md`}
    >
      <h5 className="text-[#7716BC] text-base md:text-xl font-bold tracking-tight mb-1 md:mb-3">
        {title}
      </h5>
      <p
        className="text-sm md:text-base text-justify"
        dangerouslySetInnerHTML={{ __html: description }}
      />
    </div>
  );
};

const Section4 = () => {
  // Put all cards into one array:
  const cards = [
    {
      title: "SaaS UI/UX",
      description:
        "Thoroughly studying the market and working out what this space needs moving forward. Creating wireframes and prototypes to lay out the foundational structure. Finally running iterations post user testing to iron out any inconsistencies.",
    },
    {
      title: "SaaS API Development",
      description:
        "Bridge the gap by creating APIs that allow third party apps to access your platform and communicate under a set standard/protocol.",
    },
    {
      title: "Migration Services",
      description:
        "Moving your systems to another environment with little to no disruptions. No hassle while handling different types of migration—for example, data migration, application migration, <a href='/Cloud_Services' class='text-[#7716BC] hover:underline'>cloud migration</a>, and platform migration.",
    },
    {
      title: "SaaS MVP Development",
      description:
        "In order to garner early attention and get the users attracted we create an MVP for the product/application of your choice. Acting on feedback and fine tuning the apps before full launch is always a wise decision.",
    },
    {
      title: "SaaS Growth Hacking",
      description:
        "Starting a company from scratch is easier said than done. We help businesses grow quickly by using as few resources as possible staying within budget. It's like having a cheat sheet of sorts.",
    },
    {
      title: "White Label Solutions",
      description:
        "You can always purchase a ready-to-go solution and market it as your own. Enter the market faster and save your time. Let us do the heavy lifting.",
    },
    {
      title: "SaaS Monthly Performance Benchmarks",
      description:
        "Worried how your app will turn out post release? Well don't, because we will provide <a href='/Maintenance_and_Support' class='text-[#7716BC] hover:underline'>maintenance and support services</a> for the apps/products to ensure it runs as a well oiled machine.",
    },
  ];

  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Saas Product Development Services by{" "}
        <span className="text-[#F245A1]">Valueans</span>
      </h2>

      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-6">
        {cards.map((card, index) => {
          // Every even index (0, 2, 4, ...) gets a blue background
          const bgColor = index % 2 === 1 ? "bg-blue-100" : "bg-white";

          return (
            <Card
              key={index}
              title={card.title}
              description={card.description}
              bgColor={bgColor}
            />
          );
        })}
      </div>
    </div>
  );
};

export default Section4;
