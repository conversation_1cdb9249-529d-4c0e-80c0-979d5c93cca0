import Image from "next/image";
import React from "react";

export const metadata = {
  title: "Deployment System - Valueans",
  description: "Explore our deployment system solutions for efficient and reliable software deployment processes.",
  alternates: {
    canonical: "https://valueans.com/Deployment_System",
  },
  openGraph: {
    title: "Deployment System - Valueans",
    description: "Explore our deployment system solutions for efficient and reliable software deployment processes.",
    url: "https://valueans.com/Deployment_System",
    type: "website",
  },
};

const Page = () => {
  return (
    <>
      {/* Banner Section */}
      <section>
        <div className="relative max-w-screen max-h-screen overflow-hidden">
          <Image
            src="/Images/Deployment_system_mock.png"
            alt="banner"
            layout="responsive"
            width={1440} // Replace with your image's actual width
            height={500} // Replace with your image's actual height
            objectFit="contain"
          />
        </div>
      </section>

      {/* Main Content Section */}
      <section className="w-[90vw] mx-auto my-10">
        <h2 className="text-6xl font-semibold text-center">
          Deployment System
        </h2>
        <div className="flex flex-col md:flex-row justify-center items-center gap-8 my-8">
          <p className="text-2xl font-light md:w-1/2">
            At vero eos et accusamus et iusto odio dignissimos ducimus qui
            blanditiis praesentium voluptatum deleniti atque corrupti quos
            dolores et quas molestias excepturi sint occaecati cupiditate non
            provident, similique sunt in culpa qui officia deserunt mollitia
            animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis
            est et expedita distinctio. Nam libero tempore, cum soluta nobis est
            eligendi optio cumque nihil impedit quo minus id quod maxime placeat
            facere possimus, omnis voluptas assumenda est
          </p>
          <div className="relative w-[528px] h-[345px]">
            <Image
              src="/Images/deployment_mock_2.png"
              alt="Deployment System"
              layout="fill"
              objectFit="cover"
            />
          </div>
        </div>
      </section>

      {/* "What was delivered?" Section */}
      <section className="w-full mx-auto my-14 text-center">
        <h2 className="text-4xl font-semibold text-pink-400">
          What was delivered?
        </h2>
        <div className="flex justify-between items-center">
          <div className="relative my-4 w-[10vw]  h-[600px]">
            <Image
              src="/Images/deploy_mock_3.png"
              alt="Laptop"
              layout="fill"
              objectFit="contain"
            />
          </div>
          <div className="relative my-4 w-[80vw]  h-[600px]">
            <Image
              src="/Images/deploy_mock_4.png"
              alt="HRM"
              layout="fill"
              objectFit="contain"
            />
          </div>
          <div className="relative my-4 w-[10vw]  h-[600px]">
            <Image
              src="/Images/deploy_mock_5.png"
              alt="Laptop"
              layout="fill"
              objectFit="contain"
            />
          </div>
        </div>

        <h2 className="text-3xl font-medium mt-4">Web Application</h2>
      </section>
    </>
  );
};

export default Page;
