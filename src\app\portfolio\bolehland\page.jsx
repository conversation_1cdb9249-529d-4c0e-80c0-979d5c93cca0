import React from 'react'
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";
export const metadata = {
  title: "Fast & Secure Loan App in 60 Minutes | Valueans",
  description: "FinTech application enabling users to apply for and receive loans within 60 minutes. The platform ensures secure transactions, data privacy, and a user-friendly experience.",

  alternates: {
    canonical: "https://valueans.com/portfolio/bolehland",
  }};

const page = () => {
 const images = ["/Images/Bolehland2.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/BolehLand-bg.png"} />
      <Section2
        image={"/Images/BolehLand1.png"}
        heading={"BOLEHLAND"}
        paragraph1={
          "One of our clients had a unique business idea where they wanted to make lending money easier than ever. They wanted us to develop a FinTech application where they could easily get a loan within 60 minutes. "
        }
        paragraph2={
          "Bolehland came with a lot of challenges, as it was a finance-related project, and money was the major exchange of service this business had to deliver. So our team at Valueans designed a secure application where all the transactions are transparent and the user data is kept confidential."
        }
      />
      <Section4
        paragraph1={
          "At the same time, the interface had to be extremely simple because all the processes were supposed to be completed through this single app. Therefore, we used Vue.js to make the most convenient front-end. However, due to the complex nature of this project, we chose Python for backend development. These latest technology stacks have helped us deliver a seamless application that is fast, secure, and user-friendly. "
        }
       
      />
      <Section5 images={images} />
    </div>
  )
}

export default page