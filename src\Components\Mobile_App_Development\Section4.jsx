const cards = [
  {
    title: "iOS App Development",
    description: `Let's work together to design a custom iOS app that is suited to the Apple environment. Our team creates native iOS apps that provide a smooth experience and make the most of the device's capabilities. We'll create a product keeping a human centered approach throughout the <a href='/product-management' class='text-[#7716BC] hover:underline'>product management</a> phase. Making sure it works well on all iPhones, regardless of your vision.`
  },
  {
    title: "Android App Development",
    description: `In order to fully use the Android environment, <PERSON><PERSON> creates native Android apps. We develop dependable, responsive applications that expand with your company while adhering to platform-specific design, security, and performance requirements.`
  },
  {
    title: "Consulting Services for Mobile Apps",
    description: `To begin the creation of your mobile app, we do a thorough study of your company's requirements and industry trends in order to create a backup plan. If you already have an app, we evaluate it and make specific recommendations to improve its performance. `
  },
  {
    title: "Cross-platform Mobile Development",
    description: `<a href='/Technologies/Cross_Platform_And_Hybrid_Development' class='text-[#7716BC] hover:underline'>Cross-platform applications</a> get around compatibility issues and increase market reach. We create top-notch apps with flawless user experiences for iOS and Android using Flutter and React Native. These provide improved performance, simpler maintenance, and quicker development. `
  },
  {
    title: "Mobile App Design",
    description: `Our app design and development services guarantee that the general flow, navigation, interfaces, and aesthetics of your product align with the identity and vision of your company. To guarantee smooth and intuitive user experience, we employ wireframes, prototypes, and user testing. `
  },
  {
    title: "Upgrading Existing Apps",
    description: `We propose necessary updates and new features for your mobile app based on our thorough market research, which identifies growing trends in your sector. Our professionals improve the functionality and efficiency of your app to keep your audience interested and informed.  `
  },
  {
    title: "QA and Software Testing",
    description: `To guarantee that your app offers seamless, responsive, and crash-free experience on all devices, our knowledgeable professionals conduct thorough <a href='/Quality_Assurance' class='text-[#7716BC] hover:underline'>quality assurance and usability testing.</a> Our advanced knowledge of app development allows us to identify and fix any technical problems fast.`
  },
  {
    title: "Mobile App Maintenance",
    description: `Every app we create at Valueans is handled as though it were our own. We offer <a href='/Maintenance_and_Support' class='text-[#7716BC] hover:underline'>post-deployment maintenance</a>, app enhancements, and customized feature upgrades to guarantee your app's continued performance and dependability. All of these services are designed to maintain your app's long-term quality.`
  },
];

const Card = ({ title, description }) => (
  <div className="bg-white w-full p-4 shadow-md rounded-xl overflow-hidden">
    <h3 className="text-base md:text-lg text-[#7716BC] capitalize font-semibold mb-2">
      {title}
    </h3>
    <p
      className="text-sm md:text-base text-justify"
      dangerouslySetInnerHTML={{ __html: description }}
    />
  </div>
);

const Section4 = () => (
  <div className="container mb-10 md:mb-24">
    <h2 className="text-2xl md:text-3xl md:leading-[40px] text-center font-semibold">
      <span className="text-[#F245A1]">
        Mobile App Solutions & Development 
      </span>
      <br />
      Services at Valueans
    </h2>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6 md:mt-[42px]">
      {cards.map((card, idx) => (
        <Card key={idx} {...card} />
      ))}
    </div>
  </div>
);

export default Section4;
