import Image from "next/image";
import React from "react";

export const metadata = {
  title: "Somatic Fitness - Health & Wellness App | Valueans",
  description: "Discover Somatic Fitness, our innovative health and wellness application designed to help users achieve their fitness goals through personalized programs.",
  alternates: {
    canonical: "https://valueans.com/Somatic_Fitness",
  },
  openGraph: {
    title: "Somatic Fitness - Health & Wellness App | Valueans",
    description: "Discover Somatic Fitness, our innovative health and wellness application designed to help users achieve their fitness goals through personalized programs.",
    url: "https://valueans.com/Somatic_Fitness",
    type: "website",
  },
};

const Page = () => {
  return (
    <>
      {/* Banner Section */}
      <section>
        <div className="relative max-w-screen max-h-screen overflow-hidden">
          <Image
            src="/Images/somatic_mock.png"
            alt="banner"
            layout="responsive"
            width={1440} // Replace with your image's actual width
            height={500} // Replace with your image's actual height
            objectFit="contain"
          />
        </div>
      </section>

      {/* Main Content Section */}
      <section className="w-[90vw] mx-auto my-10">
        <h2 className="text-6xl font-semibold text-center">
          Somatic Fitness App
        </h2>
        <div className="flex flex-col md:flex-row justify-center items-center gap-12 my-8">
          <p className="text-2xl font-light md:w-1/2">
            Experience the ultimate fitness journey with the Somatic Fitness
            Corporate app, a cutting-edge mobile solution designed exclusively
            for our client's dynamic fitness community. Users embark on a
            personalized wellness adventure, enjoying seamless login access to
            their tailored fitness profile. Dive into a comprehensive health and
            nutrition experience by effortlessly adding meals and crafting
            recipes that align with individual goals. The app empowers users to
            track their daily workout progress, ensuring a detailed record of
            sets completed each day. Beyond workouts, it enables meticulous
            monitoring of daily nutritional intake, offering insights into vital
            nutrients. This fitness app stands out with its intuitive interface,
            promoting a user-centric approach to health management. Whether it's
            creating a bespoke workout routine or maintaining a meticulous
            nutritional diary, the Somatic Fitness Corporate app is the perfect
            companion on the journey to optimal well-being. Elevate your fitness
            experience with this holistic mobile solution, where technology
            seamlessly integrates with your health and wellness goals.
          </p>
          <div className="flex justify-center items-center">
            <div className="relative w-[247px] h-[535px]">
              <Image
                src="/Images/somatic_mock_1.png"
                alt="Mobile"
                layout="fill"
                objectFit="cover"
              />
            </div>
            <div className="relative w-[247px] h-[535px]">
              <Image
                src="/Images/somatic_mock_2.png"
                alt="Mobile"
                layout="fill"
                objectFit="cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* "What was delivered?" Section */}
      <section className="w-[85vw] mx-auto my-20 text-center">
        <h2 className="text-4xl font-semibold text-pink-400">
          What was delivered?
        </h2>
        <div className="flex justify-center items-center my-6">
          <div className="relative my-4 w-[200px] h-[388px]">
            <Image
              src="/Images/somatic_mock_3.png"
              alt="mobile"
              layout="fill"
              objectFit="contain"
              width={200}
              height={388}
            />
          </div>
          <div className="relative my-4 w-[200px] h-[388px]">
            <Image
              src="/Images/somatic_mock_4.png"
              alt="mobile"
              layout="fill"
              objectFit="contain"
              width={200}
              height={388}
            />
          </div>
          <div className="relative my-4 w-[200px] h-[388px]">
            <Image
              src="/Images/somatic_mock_5.png"
              alt="mobile"
              layout="fill"
              objectFit="contain"
              width={200}
              height={388}
            />
          </div>
        </div>

        <h2 className="text-3xl font-medium mt-4">Android Application</h2>
      </section>
    </>
  );
};

export default Page;
