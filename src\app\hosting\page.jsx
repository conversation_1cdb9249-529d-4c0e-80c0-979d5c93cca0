
import Box from "@mui/material/Box"
import Estimate<PERSON>eader from "@/Components/EstimateHeader"

export const metadata = {
    title: "The best web hosting services and solutions ensuring unmatched performance",
    description: "Our state-of-the-art web hosting solutions ensure your applications run smoothly. High quality hosting services that cover ssl certificates, storage space, database backup etc.",
    keywords: [
      "Valueans Hosting",
      "Reliable Hosting Services",
      "High-Speed Web Hosting",
      "Business Hosting Solutions",
      "Cloud Hosting Solutions",
    ],
    alternates: {
      canonical: `https://valueans.com/hosting`,
    },
    openGraph: {
      title: "The best web hosting services and solutions ensuring unmatched performance",
      description: "Our state-of-the-art web hosting solutions ensure your applications run smoothly. High quality hosting services that cover ssl certificates, storage space, database backup etc.",
      url: "https://valueans.com/hosting",
      type: "website",
    },
  };

const Page = () => {
  return (
    <>
      <EstimateHeader title="Reliable and Scalable Hosting Solutions by Valueans" description="Experience unmatched hosting performance with Valueans. Our state-of-the-art hosting solutions ensure your applications run smoothly, securely, and with optimal uptime. Perfect for businesses of all sizes seeking high-quality hosting services." propVal={false}/>
      <Box mt={6} padding="40px">
        <stripe-pricing-table pricing-table-id={process.env.STRIPE_HOSTING_TABLE_ID}
                    publishable-key={process.env.STRIPE_PUBLISH_KEY}>
            </stripe-pricing-table>
      </Box>
    </>
  )
}

export default Page