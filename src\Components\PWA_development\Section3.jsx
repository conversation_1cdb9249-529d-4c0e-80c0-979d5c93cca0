import Image from "next/image";
import React from "react";

const Section3 = ({ image, heading, spanHeading, paragrapgh, boldPG, paragrapgh2,className }) => {
  return (
    <div className="container mb-5 px-[20px] py-5 md:py-8 ">
      <div className={`flex flex-col md:flex-row ${className} gap-4 justify-center md:justify-between items-center`}>
        <div className="w-full md:max-w-2xl">
          <h2 className="text-xl md:text-3xl md:leading-8 font-semibold mb-1">
            {heading}
            <span className="text-[#F245A1]">{spanHeading}</span>
          </h2>
          <p className="text-sm md:text-lg text-justify">{paragrapgh}</p>
          <p className="text-sm md:text-lg text-justify mt-5">{paragrapgh2}</p>
          <p className="text-sm md:text-lg text-justify font-semibold mt-3">{boldPG}</p>
        </div>
        <div className="">
          <Image src={image} alt="AI" width={400} height={400} />
        </div>
      </div>
    </div>
  );
};

export default Section3;
