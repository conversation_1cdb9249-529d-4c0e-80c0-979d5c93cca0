import Image from "next/image";

const InfoCard = ({ title, description }) => {
  return (
    <div className="w-full md:max-w-md flex items-start gap-1 border border-pink-500 p-2  rounded-lg shadow-sm">
      <div className="w-[20px] h-[20px] shrink-0">
        <Image
          src="/Images/service_frame.png"
          alt="arrow"
          width={50}
          height={50}
          className="object-contain"
        />
      </div>
      <div>
        <h3 className="font-semibold  text-base">{title}:</h3>
        <p className="text-sm text-justify">{description}</p>
      </div>
    </div>
  );
};

const InfoCard_2 = ({ title, description }) => {
  return (
    <div className="w-full md:max-w-md flex items-start gap-1 border border-pink-500 p-2  rounded-lg shadow-sm">
      <div className="w-[20px] h-[20px] shrink-0">
        <Image
          src="/Images/service_frame.png"
          alt="arrow"
          width={50}
          height={50}
          className="object-contain"
        />
      </div>
      <div>
        <h3 className="font-semibold  text-base">{title}:</h3>
        <p className="text-sm text-justify">{description}</p>
      </div>
    </div>
  );
};

const Section8 = () => {
  return (
    <div className="bg-blue-100 px-4 mb-10 md:mb-24 md:py-10 ">
      <h2 className="text-xl md:text-3xl container font-semibold text-center">
        Challenges in Adopting{" "}
        <span className="text-[#F245A1]">Low Code Automation</span>
      </h2>
      <div className="flex flex-col-reverse md:flex-row justify-center items-stretch gap-5 mt-6 md:mt-[42px]">
        <div className="w-full md:w-[60%]">
          <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6 ">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6">
              <InfoCard
                title={"Cultural Opposition to Change"}
                description={
                  "It has been said that 'professional' developers ignore low-level code for those who lack skill. As a result, stigma and doubt over the effects of low-code application development are fostered. Developers often perceive disruptive automation technologies as a danger, even in cases where there is no malicious intent."
                }
              />
              <InfoCard
                title={"Adoption Challenges"}
                description={
                  "A minimal code development platform's interface, language, and overall experience might differ greatly from what developers are accustomed to. There may be an off-putting learning curve. Low code adoption may become irrelevant if developers continue to hand-code in the absence of proper training and continuous assistance."
                }
              />
            </div>
            <div className="flex justify-center">
              <InfoCard
                title={"Integrability"}
                description={
                  "Businesses in transition require the low code platform to function in unison with their current (legacy) software. As a result, the minimal code development platform must include custom architecture for integrating with databases, applications, and outside services. Low code cannot provide the value it can without this. Middleware or special coding may be needed to fix this, which is frequently more work than it is worth. "
                }
              />
            </div>
            <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
              <InfoCard_2
                title={"Issues with Teething"}
                description={
                  "Low code solutions created subpar code and provided awful development experiences before becoming the reliable tools they are now. Early adopters have been deterred by this."
                }
              />
              <InfoCard_2
                title={"Scalability"}
                description={
                  "Prototypes or short apps created for internal usage are the main emphasis of low code platforms for citizen developers. The performance requirements for corporate applications are not met by them."
                }
              />
            </div>
          </div>
        </div>
        <div className="relative w-full md:w-[30%] min-h-[250px] md:min-h-0">
          {/* Use fill + object-cover to cover the entire container */}
          <Image
            src={"/Images/LowCode4.jpeg"}
            alt="AI"
            fill
            className="object-cover"
          />
        </div>
      </div>
    </div>
  );
};

export default Section8;
