import React from "react";
import IndustriesCard from "./IndustriesCard";

const Section10 = () => {
  return (
    <div className="container mb-10 md:mb-24">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        Industries We Serve Valueans
      </h2>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <IndustriesCard
            imgSrc={"/Images/healthcare.png"}
            altText={"healthcare"}
            title={"Healthcare"}
            description={
              " We use data to help patients get better results and make <a href='/health-care-development' class='text-[#7716BC] hover:underline'>enterprise healthcare solutions</a> smoother and more efficient."
            }
          />
          <IndustriesCard
            imgSrc={"/Images/finance.png"}
            altText={"Finance"}
            title={"Finance"}
            description={
              "We support technology in the <a href='/financial-app-development' class='text-[#7716BC] hover:underline'> finance industry</a> through risk management, regulatory compliance, and investment decisions."
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <IndustriesCard
            imgSrc={"/Images/Retail.png"}
            altText={"Retail"}
            title={"Retail"}
            description={
              "We make inventory management better, improve the customer experience, and boost sales growth"
            }
          />
          <IndustriesCard
            imgSrc={"/Images/Manufacturing.png"}
            altText={"Manufacturing"}
            title={"Manufacturing"}
            description={
              "We assist the <a href='/Industries/Manufacturing' class='text-[#7716BC] hover:underline'>manufacturing sector</a> by making production smoother, improve supply chain management, and ensure top quality."
            }
          />
          <IndustriesCard
            imgSrc={"/Images/education.png"}
            altText={"Education"}
            title={"Education"}
            description={
              "We Analyze student performance, improving resource allocation, and enhancing <a href='/Industries/Education' class='text-[#7716BC] hover:underline'>education industry solutions</a>."
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section10;
