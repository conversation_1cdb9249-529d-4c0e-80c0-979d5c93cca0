// Site Configuration
// Updated to use production domain valueans.com

export const siteConfig = {
  // Production URL - now live at valueans.com
  url: "https://valueans.com",

  // Legacy Vercel URL (for reference)
  legacyUrl: "https://kapoorsoftwaresolutions-git-master-valueans-projects.vercel.app",

  // Site information
  name: "Valueans",
  description: "Valueans provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.",

  // Social links
  social: {
    twitter: "https://twitter.com/valueans",
    linkedin: "https://www.linkedin.com/company/valueans",
  },
  
  // SEO settings
  seo: {
    defaultTitle: "Kapoor Software Solutions - Custom Software Development Services",
    titleTemplate: "%s | Kapoor Software Solutions",
    defaultDescription: "Kapoor Software Solutions provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.",
    keywords: [
      "Custom Software Development",
      "Mobile App Development", 
      "Web Development",
      "AI/ML Solutions",
      "Digital Transformation",
      "Software Solutions",
      "Technology Services",
      "Kapoor Software Solutions",
    ],
  },
};

// Helper function to get the current base URL
export const getBaseUrl = () => {
  return siteConfig.url;
};

// Helper function to get full URL for a path
export const getFullUrl = (path = "") => {
  const baseUrl = getBaseUrl();
  return `${baseUrl}${path.startsWith('/') ? path : `/${path}`}`;
};

// Helper function for when moving to production
export const switchToProduction = () => {
  // This function can be used to easily switch all URLs to production
  // Just update siteConfig.url to siteConfig.productionUrl
  console.log("To switch to production, update siteConfig.url to:", siteConfig.productionUrl);
};
