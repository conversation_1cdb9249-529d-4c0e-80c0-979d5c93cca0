import React from "react";

const Section4 = ({
  heading,
  spanheading,
  cardData,
  image,
  headingLeft,
  paragraph,
}) => {
  return (
    <div className="bg-[#1F62EA26] py-5">
      <div className="container mb-10 md:mb-24">
        <div className="my-4">
          <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
            {heading} <span className="text-[#F245A1]">{spanheading}</span>{" "}
            {headingLeft}
          </h2>
          <p className="md:w-[75%] md:mx-auto text-base md:text-xl text-center">
            {paragraph}{" "}
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 w-full gap-8">
          {cardData.map((card, index) => (
            <div
              key={index}
              className="border border-[#F245A1] rounded-lg bg-white p-4 min-w-lg mx-auto"
            >
              <h2 className="text-base my-2 font-semibold">{card.title}</h2>
              <p className="text-sm mb-2 text-justify">{card.description}</p>

              {/* List mapping here */}
              <ul className="list-disc list-inside text-sm text-gray-700 mt-3">
                {card.list.map((item, idx) => (
                  <li key={idx} dangerouslySetInnerHTML={{ __html: item }} />
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section4;
